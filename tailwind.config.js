/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './App.{js,jsx,ts,tsx}',
    './app/**/*.{js,jsx,ts,tsx}', // For Expo Router structure
    './Components/**/*.{js,jsx,ts,tsx}', // Adjusted to lowercase 'components'
    './Layouts/**/*.{js,jsx,ts,tsx}', // Adjusted to lowercase 'layouts'
  ],

  theme: {
    extend: {},
    colors: {
      primary: '#172554',
      secondary: '#712CFB',
      tertiary: '#573D99',
      blue: 'rgb(59 130 246)',
      gray: '#D1D1D6',
      gray2: '#C6C6CC',
      ArrowColor: '#3C3C43',
      white: '#fff',
      lightWhite: '#F6F6F6',
      grayText: '#8E8E93',
      danger: '#FF3B30',
      black: '#000000',
    },
  },
  plugins: [],
};
