import { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Text,
  ImageBackground,
  KeyboardAvoidingView,
  SafeAreaView,
  StyleSheet,
  Dimensions,
  View,
  Platform,
  ScrollView,
} from 'react-native';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';
import { Button, IconButton, Modal, Portal } from 'react-native-paper';

import CustomInput from '@/Components/CustomInputs/CustomInput';
import { COLORS, SIZES } from '@/Constants';
import Loading from '@/Layouts/Loading/Loading';
import { useAuthStore } from '@/stores/useAuthStore';
import { yupResolver } from '@hookform/resolvers/yup';
import i18n from 'i18n';
import * as Yup from 'yup';

import Entypo from '@expo/vector-icons/Entypo';
import { useRouter } from 'expo-router';

import useLogin from '@/Hooks/AuthHooks/useLogin';

import {
  AddItemToSecureStore,
  GetItemFromSecureStore,
} from '@/utils/secureStore/HandleSecureStore';
import { customToast } from '@/utils/toast/';

const { width } = Dimensions.get('screen');

export interface IFrom {
  phone: string;
  password: string;
  companyKey: string;
}
export interface IErrors {
  phone: string;
  password: string;
  companyKey: string;
}

const validationSchema = Yup.object().shape({
  phone: Yup.string()
    .required('Phone number is required')
    .min(8, 'Phone number must be at least 8 characters'),
  password: Yup.string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters'),
  companyKey: Yup.string()
    .required('Company key is required')
    .min(2, 'Company key must be at least 2 characters'),
});
type IForm = Yup.InferType<typeof validationSchema>;

const LoginScreen = () => {
  const { t } = useTranslation();
  const { setUser, setToken } = useAuthStore();
  const router = useRouter();

  const {
    control,
    formState: { errors },
    setValue,
    handleSubmit,
    getValues,
  } = useForm<IForm>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      phone: '',
      password: '',
      companyKey: '',
    },
  });

  const { mutate, isPending } = useLogin();

  const [showModal, setShowModal] = useState<boolean>(false);
  const [language, setLanguage] = useState<'ar' | 'en'>('en');

  const onSubmit = handleSubmit(values => {
    mutate(values, {
      onSuccess: data => {
        if (data) {
          setUser(data.user);
          setToken(data.token);
          router.push('/(tabs)');
        }
      },
      onError: error => {
        customToast(error.message, 'error');
      },
    });
  });

  const handleLanguageChange = useCallback(() => {
    const newLanguage = language === 'ar' ? 'en' : 'ar';
    i18n.changeLanguage(newLanguage).then(() => setLanguage(newLanguage));
  }, [language]);

  const handleSaveCompanyKeyToLocalStorage = async () => {
    const companyKey = getValues('companyKey');
    if (!companyKey) {
      Toast.show({
        title: t('company-key-required'),
        type: ALERT_TYPE.DANGER,
        autoClose: 3000,
      });
      setShowModal(true);
      return;
    }
    try {
      await AddItemToSecureStore('companyKey', companyKey);
      setShowModal(false);
    } catch (error: any) {
      Toast.show({
        title: error.message,
        type: ALERT_TYPE.DANGER,
        autoClose: 3000,
      });
    }
  };

  // Fetch company key from secure storage and app settings on component mount
  useEffect(() => {
    const getCompanyKey = async () => {
      const companyKey = await GetItemFromSecureStore('companyKey');
      if (companyKey) {
        setValue('companyKey', companyKey);
      } else {
        setShowModal(true);
        return;
      }
    };

    const getAppSettings = async () => {
      const settings = await GetItemFromSecureStore('appSettings');
      if (settings) {
        const appSettings = JSON.parse(settings) as {
          isArabic: boolean;
          accessCode: boolean;
        };
        const language = appSettings.isArabic ? 'ar' : 'en';

        i18n.changeLanguage(language).then(() => setLanguage(language));
      }
    };

    getCompanyKey();
    getAppSettings();
  }, []);

  return (
    <>
      {isPending && <Loading />}
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1 }}
        >
          <ScrollView
            style={styles.scrollContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            <Button
              buttonColor={COLORS.blue}
              mode="contained"
              shouldRasterizeIOS={true}
              className="w-20 rounded-none align-self-end"
              onPress={handleLanguageChange}
            >
              <Entypo name="language" size={24} color={COLORS.white} />
            </Button>

            <View className="my-2">
              <ImageBackground
                source={require('@/assets/login.jpg')}
                className="w-[100%] h-[150px] "
                resizeMode="contain"
              />
              <Text className=" text-xl font-bold mt-2  text-center max-w-[70%] mx-auto">
                {t('welcome')}{' '}
                <Text className="font-extrabold text-blue-500 ">
                  {t('app-name')}{' '}
                </Text>
                {t('mobile-attendance-application')}
              </Text>
            </View>

            <Controller
              control={control}
              name="phone"
              render={({ field: { onChange, value } }) => (
                <CustomInput
                  value={value}
                  handleChangeText={onChange}
                  placeholder={t('user-phone')}
                  errorText={errors.phone?.message}
                  keyboardType="numeric"
                />
              )}
            />

            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, onBlur, value } }) => (
                <CustomInput
                  value={value}
                  handleChangeText={onChange}
                  placeholder={t('password')}
                  errorText={errors.password?.message}
                  secureTextEntry={true}
                />
              )}
            />

            <Button
              onPress={onSubmit}
              disabled={isPending}
              buttonColor={COLORS.blue}
              textColor={COLORS.white}
              mode="contained"
              className="mt-5 rounded-sm"
            >
              {t('login')}
            </Button>
          </ScrollView>
        </KeyboardAvoidingView>

        <IconButton
          onPress={() => setShowModal(true)}
          style={styles.settingsButton}
          containerColor={COLORS.blue}
          iconColor={COLORS.white}
          mode="contained"
          icon="cellphone-settings"
        />

        <Portal>
          <Modal
            contentContainerStyle={styles.modalStyle}
            visible={showModal}
            onDismiss={() => setShowModal(false)}
          >
            <View style={{ gap: SIZES.large }}>
              <Text style={styles.modalHeader}>{t('company-key')}</Text>
              <Controller
                control={control}
                name="companyKey"
                render={({ field: { onChange, value } }) => (
                  <CustomInput
                    value={value}
                    handleChangeText={onChange}
                    placeholder={t('company-key')}
                    errorText={errors.companyKey?.message}
                  />
                )}
              />

              <Button
                mode="contained"
                onPress={handleSaveCompanyKeyToLocalStorage}
                buttonColor={COLORS.primary}
                textColor={COLORS.white}
                style={{ borderRadius: 5, marginVertical: SIZES.small }}
              >
                {t('save')}
              </Button>
            </View>
          </Modal>
        </Portal>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    justifyContent: 'center',
  },

  scrollContainer: {
    flex: 1,
    padding: SIZES.small,
  },
  settingsButton: {
    borderRadius: 50,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 10,
    right: 20,
  },
  modalStyle: {
    backgroundColor: COLORS.white,
    width: width - 30,
    padding: SIZES.large,
    marginHorizontal: 'auto',
  },
  modalHeader: {
    textAlign: 'center',
    fontWeight: '600',
    fontSize: SIZES.xLarge,
  },
});

export default LoginScreen;
