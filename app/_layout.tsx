
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import { AlertNotificationRoot } from 'react-native-alert-notification';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { PaperProvider } from 'react-native-paper';
import { Provider } from 'react-redux';

import { useAuthStore } from '@/stores/useAuthStore';

import { Stack } from 'expo-router';

import { store } from '../Redux/Store';
import UserInActivityProvider from '../context/UserInActivity';
import i18n from '../i18n';

const queryClient = new QueryClient();

const _layout = () => {
  const { isLoggedIn } = useAuthStore();

  return (
    <I18nextProvider i18n={i18n}>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <PaperProvider>
              <AlertNotificationRoot>
                <UserInActivityProvider>
                  <Stack screenOptions={{ headerShown: false }}>
                    <Stack.Protected guard={isLoggedIn}>
                      <Stack.Screen name="(tabs)" />
                      <Stack.Screen name="(screens)" />
                    </Stack.Protected>
                    <Stack.Protected guard={!isLoggedIn}>
                      <Stack.Screen name="(auth)" />
                    </Stack.Protected>
                  </Stack>
                </UserInActivityProvider>
              </AlertNotificationRoot>
            </PaperProvider>
          </GestureHandlerRootView>
        </Provider>
      </QueryClientProvider>
    </I18nextProvider>
  );
};

export default _layout;

