import { useCallback, useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';

import AttendanceLocationsList from '@/Components/AttendanceLocationsList/AttendanceLocationsList';
import CheckInComponent from '@/Components/ChekinComponent/CheckInComponent';
import HomeActionsList from '@/Components/HomeActionsList/HomeActionsList';
import HomeHeader from '@/Components/HomeHeader/HomeHeader';
import { COLORS } from '@/Constants';
import Loading from '@/Layouts/Loading/Loading';
import { useAppDispatch } from '@/Redux/Store';
import { useAuthStore } from '@/stores/useAuthStore';

import { StatusBar } from 'expo-status-bar';

import { useLocation } from '@/Hooks/useLocation';
import useGetUserByID from '@/Hooks/userHooks/useGetUserByID';
import usePushNotifications from 'Hooks/usePushNotifications';

import { customToast } from '@/utils/toast';

const HomeScreen = () => {
  const dispatch = useAppDispatch();
  const { user } = useAuthStore();

  // To refetch user data by ID
  const {} = useGetUserByID(user?._id || '');

  const { registerPushNotificationsAsync, expoPushToken } =
    usePushNotifications();

  const { loading: getLocationLoading, GetUserCurrentLocation } = useLocation();

  const [loading, setLoading] = useState<boolean>(false); // Set to true initially for first load
  const [refreshLoading, setRefreshLoading] = useState<boolean>(false);

  // Location fetching with loading states properly handled
  const handleRefresh = useCallback(async () => {
    setRefreshLoading(true);
    setLoading(true);
    try {
      if (!user?._id) return;
      GetUserCurrentLocation(); // Fetch location
    } catch (err: any) {
      console.error('Error during location fetch:', err);
      customToast(err?.message || 'Failed to fetch location', 'error');
    } finally {
      setRefreshLoading(false);
      setLoading(false);
    }
  }, [dispatch, GetUserCurrentLocation, user?._id]);

  useEffect(() => {
    registerPushNotificationsAsync();
  }, []);

  return (
    <>
      {(getLocationLoading || refreshLoading || loading) && <Loading />}

      <StatusBar hidden={true} />

      <View style={{ flex: 1 }}>
        <View style={styles.headerStyles}>
          <HomeHeader />
          <CheckInComponent />
          <HomeActionsList />
        </View>

        <AttendanceLocationsList />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  headerStyles: {
    borderBottomEndRadius: 80,
    borderBottomStartRadius: 80,
    backgroundColor: COLORS.primary,
  },
});

export default HomeScreen;
