import React, { useCallback, useEffect, useState, memo } from 'react';
import { View, SafeAreaView, Text } from 'react-native';
import { Avatar, Divider } from 'react-native-paper';

import { FlashList } from '@shopify/flash-list';
import CustomAvatar from 'Components/CustomAvatar';

import { Link, useRouter } from 'expo-router';

import { axiosRequest } from '@/utils/useAxiosFetch';
import { socket } from '@/utils/useSocket';

import EmptyTasksComponent from '@/Components/EmptyTasksComponent';
import TicketItem from '@/Components/TicketItem/TicketItem';
import { COLORS, globalStyles, SIZES } from '@/Constants';
import CustomHeader from '@/Layouts/CustomHeader/CustomHeader';
import Loading from '@/Layouts/Loading/Loading';
import { useAppSelector } from '@/Redux/Store';
import { socketEventsList } from '@/SocketEvents';

export interface IMessageTypes {
  _id: string;
  title: string;
  body: string;
  status: 'pending' | 'rejected';
  priority: 'high' | 'normal' | 'medium';
  sender: {
    fullName: string;
    userName: string;
    companyName: string;
    phone: string;
    _id: string;
    company: string;
  };
  modified: boolean;
  createdAt: Date;
  updatedAt: Date;
  __v: 0;
  receivers: { _id: string; avatar: string; fullName: string; phone: string }[];
  replay: string;
}

function TicketsListsScreen() {
  const { data } = useAppSelector(s => s.user);
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);
  const [tickets, setTickets] = useState<IMessageTypes[]>([]);

  const [pagination, setPagination] = useState({
    skip: 0,
    limit: 5,
    length: 0,
  });

  const ChatHeaderRight = () => (
    <Link href="(screens)/UserProfileScreen">
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: SIZES.xSmall,
        }}
      >
        <Text style={{ fontSize: SIZES.medium, color: COLORS.white }}>
          {data?.fullName}
        </Text>
        <CustomAvatar
          fullName={data?.fullName}
          avatar={data?.avatar}
          size={70}
        />
      </View>
    </Link>
  );

  // Fetch tickets
  const getTickets = useCallback(() => {
    setLoading(true);
    axiosRequest({
      url: `/tickets?skip=${pagination.skip}&limit=${pagination.limit}`,
    })
      .then(response => {
        if (response?.data?.data) {
          // Reverse the array once when fetched
          if (tickets.length > 0) {
            setTickets(prev => [...prev, ...response.data.data?.reverse()]);
          } else {
            setTickets(response.data.data.reverse());
          }
          setPagination(prev => ({ ...prev, length: response.data.length }));
        }
      })
      .catch(err => console.log('Get Tickets Error', err))
      .finally(() => setLoading(false));
  }, [pagination.limit, pagination.limit]);

  const setTicketStatus = async (id: string) => {
    try {
      axiosRequest({
        url: `/tickets/${id}`,
        method: 'PUT',
        data: {
          status: 'open',
        },
      });
    } catch (error) {
      console.log('Set ticketStatus error', error);
    }
  };

  // Refresh handler
  const handleRefresh = useCallback(() => {
    setPagination({
      length: 0,
      limit: 5,
      skip: 0,
    });
    getTickets();
  }, []);

  const handleLoadMore = useCallback(() => {
    if (tickets.length === 0) return;
    if (tickets.length < pagination.length) {
      console.log('can fetch', tickets.length, pagination.length);
      setPagination(prev => ({
        ...prev,
        limit: prev.limit + 5,
        skip: prev.skip + 5,
      }));
    }
  }, [tickets.length, pagination.length]);

  // Render ticket item, memoized for optimization
  const renderTicketItem = useCallback(
    ({ item }: { item: IMessageTypes }) => (
      <MemoizedTicketItem
        {...item}
        onPress={() => {
          router.navigate({
            pathname: `/ChatScreen`,
            params: { _id: item._id },
          });
          setTicketStatus(item._id);
        }}
      />
    ),
    [router]
  );

  // Socket event listener to receive new tickets
  useEffect(() => {
    if (socket) {
      const handleReceiveMessage = (message: any) => {
        setTickets(prev => [message, ...prev]); // New message at the top
        console.log('Ticket Received');
      };

      socket.on(socketEventsList.tickets.create.on, handleReceiveMessage);

      return () => {
        socket.off(socketEventsList.tickets.create.on, handleReceiveMessage);
      };
    }
  }, []);

  // Fetch tickets on component mount
  useEffect(() => {
    getTickets();
  }, [getTickets]);

  return (
    <>
      {loading && <Loading />}
      <CustomHeader showBackButton={false} headerRight={ChatHeaderRight} />
      <SafeAreaView style={globalStyles.SafeAreaView}>
        <View className="mt-5 py-2 px-4 flex-1 min-h-[200px]">
          <FlashList
            onRefresh={handleRefresh}
            refreshing={loading}
            estimatedItemSize={138}
            ListEmptyComponent={() => {
              return !loading ? <EmptyTasksComponent /> : <></>;
            }}
            alwaysBounceVertical={true}
            bounces={true}
            data={tickets}
            keyExtractor={item => item?._id}
            renderItem={renderTicketItem}
            onEndReachedThreshold={0.1} // Trigger load more when 50% from bottom
            onEndReached={handleLoadMore}
          />
        </View>
      </SafeAreaView>
    </>
  );
}

// Memoized Ticket Item component to avoid re-renders
const MemoizedTicketItem = memo(
  ({ onPress, ...item }: IMessageTypes & { onPress: () => void }) => (
    <View style={{ marginBottom: SIZES.xSmall }}>
      <TicketItem {...item} onPress={onPress} />
      <Divider />
    </View>
  )
);

export default TicketsListsScreen;
