import { useEffect } from 'react';

import { Feather, FontAwesome5, Ionicons, Entypo } from '@expo/vector-icons';
import { Tabs } from 'expo-router';

import useSocketNotifications from '@/Hooks/useSocketNotifications';
import { useLocation } from 'Hooks/useLocation';

import { COLORS, SIZES } from '@/Constants';

const TabBarIcon = ({
  name,
  size,
  color,
  library,
}: {
  name: any;
  size: number;
  color: string;
  library: string;
}) => {
  switch (library) {
    case 'Feather':
      return <Feather name={name} size={size} color={color} />;
    case 'FontAwesome5':
      return <FontAwesome5 name={name} size={size} color={color} />;
    case 'Ionicons':
      return <Ionicons name={name} size={size} color={color} />;
    case 'Entypo':
      return <Entypo name={name} size={size} color={color} />;
    default:
      return null;
  }
};

const TabsLayout = () => {
  useSocketNotifications();
  const { GetUserCurrentLocation } = useLocation();

  const screenOptions = ({ route }: { route: any }) => ({
    tabBarIcon: ({ focused }: { focused: boolean }) => {
      let iconName, library;

      switch (route.name) {
        case 'index':
          iconName = 'home';
          library = 'Ionicons';
          break;
        case 'tickets':
          iconName = 'message-square';
          library = 'Feather';
          break;
        case 'profile':
          iconName = 'user-circle';
          library = 'FontAwesome5';
          break;
        default:
          iconName = 'home';
          library = 'Ionicons';
      }
      useSocketNotifications();
      return (
        <TabBarIcon
          name={iconName}
          size={SIZES.xLarge}
          color={focused ? COLORS.secondary : COLORS.grayText}
          library={library}
        />
      );
    },
    headerShown: false,
    tabBarShowLabel: false,
  });
  useEffect(() => {
    GetUserCurrentLocation();
  }, []);
  return (
    <Tabs screenOptions={screenOptions}>
      <Tabs.Screen name="index" />
      <Tabs.Screen name="tickets" />
      <Tabs.Screen name="profile" />
    </Tabs>
  );
};

export default TabsLayout;
