import React from 'react';
import { Text, TouchableOpacity, FlatList, View } from 'react-native';

import ProfileScreenItem from '@/Components/ProfileScreenItem/ProfileScreenItem';
import { COLORS } from '@/Constants';
import { useAuthStore } from '@/stores/useAuthStore';
import CustomAvatar from 'Components/CustomAvatar';
import { t } from 'i18next';

import {
  MaterialIcons,
  AntDesign,
  EvilIcons,
  Foundation,
  Feather,
} from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const screenOptions = [
  {
    id: '1',
    title: 'profile',
    icon: <AntDesign name="user" size={24} color={COLORS.primary} />,
    navigate: 'UserProfileScreen',
    navigateMethod: 'navigate',
  },
  {
    id: '2',
    title: 'settings',
    icon: <EvilIcons name="gear" size={24} color={COLORS.primary} />,
    navigate: 'SettingsScreen',
    navigateMethod: 'navigate',
  },
  {
    id: '3',
    title: 'terms-and-conditions',
    icon: (
      <Foundation name="clipboard-notes" size={24} color={COLORS.primary} />
    ),

    navigate: 'TermsAndConditionsScreen',
    navigateMethod: 'navigate',
  },
  {
    id: '4',
    title: 'privacy-and-policy',
    icon: <MaterialIcons name="privacy-tip" size={24} color={COLORS.primary} />,
    navigate: 'PrivacyAndPolicyScreen',
    navigateMethod: 'navigate',
  },
];

const ProfileScreen = () => {
  const router = useRouter();
  const { logout, user } = useAuthStore();
  return (
    <View className="flex-1  items-center bg-white">
      {/* Avatar */}
      <View
        style={{ backgroundColor: COLORS.primary }}
        className="w-full pt-16 items-center"
      >
        <TouchableOpacity
          className=" items-center"
          onPress={() => {
            router.navigate('UserProfileScreen');
          }}
        >
          <CustomAvatar
            avatar={user?.avatar}
            fullName={user?.fullName || ''}
            size={120}
            containerStyles={{ borderWidth: 4 }}
          />
        </TouchableOpacity>
        <Text className="text-white font-semibold text-2xl">
          {user?.fullName}
        </Text>
        <Text className="text-gray font-bold text-lg ">
          {user?.jobDescription}
        </Text>
      </View>

      {/* Button */}
      <TouchableOpacity
        onPress={() => router.navigate('UserProfileScreen')}
        className="py-3 rounded w-[90%] px-5 mt-5"
        style={{ backgroundColor: COLORS.secondary }}
      >
        <Text className="text-white font-bold text-center text-xl">
          Edit My Profile
        </Text>
      </TouchableOpacity>
      {/* List of Item */}
      <FlatList
        className="w-full mt-5"
        data={screenOptions}
        keyExtractor={item => item.id}
        renderItem={({ item }) => {
          return (
            <View className="mb-2">
              <ProfileScreenItem
                icon={item.icon}
                title={t(item.title)}
                onPress={() => {
                  router.navigate(`(screens)/${item?.navigate}`);
                }}
              />
            </View>
          );
        }}
      />
      <View className="w-full">
        <ProfileScreenItem
          icon={<Feather name="log-out" size={24} color="red" />}
          title={t('sign-out')}
          textColor="red"
          onPress={logout}
        />
      </View>
    </View>
  );
};

export default ProfileScreen;
