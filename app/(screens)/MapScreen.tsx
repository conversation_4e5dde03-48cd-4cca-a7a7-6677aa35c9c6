import { useRoute } from '@react-navigation/native';
import React, { useState, useEffect, useRef } from 'react';
import { Image, Platform, SafeAreaView, StyleSheet, View } from 'react-native';
import MapView, { Callout, Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { Text } from 'react-native-paper';

import { useLocation } from '@/Hooks/useLocation';

import placeholderImage from '@/assets/placeholder-image.jpg';

import { COLORS } from '@/Constants';
import Loading from '@/Layouts/Loading/Loading';
import { useAppSelector } from '@/Redux/Store';
import { IMAGE_URL } from '@/config';

interface IParams {
  title: string;
  description?: string;
  lat: number;
  lng: number;
  radius: number;
  _id: string;
}

const MapScreen = () => {
  const mapRef = useRef<MapView>(null);
  const route = useRoute();
  const user = useAppSelector(s => s.user.data);

  const params = route.params as IParams;
  const [data, setData] = useState<IParams | undefined>(undefined);
  const { location } = useLocation();
  useEffect(() => {
    if (params) {
      const latitude = Number(params.lat ?? location?.coords?.latitude);
      const longitude = Number(params.lng ?? location?.coords?.longitude);

      setData({
        ...params,
        lat: latitude,
        lng: longitude,
      });

      if (mapRef.current) {
        mapRef.current.animateCamera(
          {
            center: {
              latitude,
              longitude,
            },
            zoom: 10,
          },
          { duration: 2000 }
        );
      }
    }
  }, [params]);

  if (!data) {
    return <Loading />;
  }

  return (
    <>
      <View style={{ flex: 1 }}>
        <MapView
          ref={mapRef}
          style={StyleSheet.absoluteFill}
          mapType="terrain"
          showsMyLocationButton={true}
          showsUserLocation={true}
          provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
          initialRegion={{
            latitude: data?.lat,
            longitude: data?.lng,
            latitudeDelta: 0.3,
            longitudeDelta: 0.3,
          }}
        >
          {/* <Circle
            radius={data.radius ?? 10}
            center={{latitude: data.lat, longitude: data.lng}}
          /> */}
          <Marker coordinate={{ latitude: data.lat, longitude: data.lng }}>
            <Callout>
              <View className="justify-center items-center p-2 max-w-[200px]">
                <Text className="font-bold  text-secondary mb-1 text-xl ">
                  {data?.title ?? ''}
                </Text>
                <Text className="text-lg font-medium">
                  {' '}
                  {data.description}{' '}
                </Text>

                <Image
                  source={
                    user?.avatar?.length > 0
                      ? { uri: `${IMAGE_URL}${user.avatar}` }
                      : placeholderImage
                  }
                  style={{
                    borderWidth: 2,
                    borderColor: COLORS.primary,
                    objectFit: 'cover',
                    width: 30,
                    height: 30,
                    backgroundColor: COLORS.secondary,
                    position: 'relative',
                  }}
                />

                <Text className="text-lg font-semibold text-center">
                  {user?.fullName}
                </Text>
              </View>
            </Callout>
          </Marker>
        </MapView>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    backgroundColor: COLORS.primary,
  },
});

export default MapScreen;
