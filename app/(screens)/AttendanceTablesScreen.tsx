import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, FlatList } from 'react-native';

import { COLORS, SIZES } from '@/Constants';
import { useAuthStore } from '@/stores/useAuthStore';
import i18n from 'i18n';

import { Period, TimeTable } from 'types/Index';

const AttendanceTableScreen = () => {
  const { user } = useAuthStore();
  const timeTables = user?.timeTables;

  const renderItem = ({ item }: { item: TimeTable }) => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.title}>{item?.title}</Text>
      </View>

      {item?.startDate && (
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>
            {`${i18n.t('start-date')}: ${new Date(
              item.startDate
            ).toLocaleDateString()}`}
          </Text>
          <Text style={styles.dateText}>
            {`${i18n.t('end-date')}: ${
              item.endDate ? new Date(item.endDate).toLocaleDateString() : '-'
            }`}
          </Text>
        </View>
      )}

      {item.periods?.map((period: Period, index: number) => (
        <View key={index} style={styles.periodContainer}>
          <View style={styles.periodHeader}>
            <Text style={styles.periodTitle}>
              {`${i18n.t('period')} - ${index + 1}`}
            </Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{i18n.t('attend-time')}:</Text>
            <Text style={styles.value}>{period.startTime}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{i18n.t('attend-notice')}:</Text>
            <Text style={styles.noticeText}>{period.startNotice || '-'}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{i18n.t('leave-time')}:</Text>
            <Text style={styles.value}>{period.endTime}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>{i18n.t('leave-notice')}:</Text>
            <Text style={styles.noticeText}>{period.endNotice || '-'}</Text>
          </View>

          <View style={styles.daysContainer}>
            {(period as Period).daysOfWeek?.map((day, dayIndex) => (
              <View key={dayIndex} style={styles.dayChip}>
                <Text style={styles.dayText}>{day}</Text>
              </View>
            ))}
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={timeTables}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        contentContainerStyle={styles.listContent}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  listContent: {
    padding: SIZES.medium,
  },
  card: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.small,
    padding: SIZES.medium,
    marginBottom: SIZES.medium,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray2,
    paddingBottom: SIZES.small,
    marginBottom: SIZES.small,
  },
  title: {
    fontSize: SIZES.medium,
    fontWeight: 'bold',
    color: COLORS.primary,
    textAlign: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.medium,
  },
  dateText: {
    fontSize: SIZES.large,
    color: COLORS.gray,
  },
  periodContainer: {
    marginTop: SIZES.small,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray2,
    paddingTop: SIZES.medium,
  },
  periodHeader: {
    marginBottom: SIZES.small,
  },
  periodTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.secondary,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.small,
  },
  label: {
    fontSize: SIZES.small,
    color: COLORS.black,
    fontWeight: '600',
  },
  value: {
    fontSize: SIZES.medium,
    color: COLORS.black,
  },
  noticeText: {
    fontSize: SIZES.medium,
    color: COLORS.danger,
    fontStyle: 'italic',
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: SIZES.small,
  },
  dayChip: {
    backgroundColor: COLORS.secondary,
    borderRadius: SIZES.large,
    paddingVertical: SIZES.small,
    paddingHorizontal: SIZES.xSmall,
    margin: SIZES.xSmall,
  },
  dayText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
});

export default AttendanceTableScreen;
