import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  Text,
  Linking,
  Alert,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Platform,
} from 'react-native';
import { Toast, ALERT_TYPE } from 'react-native-alert-notification';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSharedValue } from 'react-native-reanimated';

import BottomSheet from '@/Components/BottomSheet/BottomSheet';
import CustomInput from '@/Components/CustomInputs/CustomInput';
import { COLORS, SIZES } from '@/Constants';
import Loading from '@/Layouts/Loading/Loading';
import { useAuthStore } from '@/stores/useAuthStore';
import CustomAvatar from 'Components/CustomAvatar';
import validator from 'validator';

import { MaterialIcons, Entypo } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';

import useUpdateUser from '@/hooks/userHooks/useUpdateUser';

import { axiosRequest } from '@/utils/useAxiosFetch';

const UserProfileScreen = () => {
  const { user } = useAuthStore();
  const { mutate } = useUpdateUser(user?._id || '');
  const { t } = useTranslation();

  const [form, setForm] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    avatar: '',
  });
  const [errors, setErrors] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    avatar: '',
  });
  const [loading, setLoading] = useState(false);
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [outsource, setOutsource] = useState(false);

  const offset = useSharedValue(0);
  const [libraryPermission, requestLibraryPermission] =
    MediaLibrary.usePermissions();

  useEffect(() => {
    if (user?._id) {
      setForm({
        fullName: user?.fullName || '',
        email: user?.email || '',
        phone: user?.phone || '',
        address: user?.address || '',
        avatar: user?.avatar || '',
      });
    }
  }, [user?._id]);

  const handleValidation = (name: keyof typeof form, value: any): boolean => {
    let isValid = true;
    let error = '';

    switch (name) {
      case 'fullName':
        isValid = value.trim().length > 0;
        error = isValid ? '' : t('user-name-is-required');
        break;
      case 'phone':
        isValid = validator.isMobilePhone(value);
        error = isValid ? '' : t('phone-number-is-required');
        break;
    }

    setErrors(prev => ({ ...prev, [name]: error }));
    return isValid;
  };

  const handleChange = (name: keyof typeof form, value: string) => {
    setForm(prev => ({ ...prev, [name]: value }));
    handleValidation(name, value);
  };

  const handleImagePicker = async () => {
    const { status } = await requestLibraryPermission();
    if (status !== 'granted') {
      Alert.alert(t('permission-required'));
      Linking.openSettings();
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled && result.assets?.length) {
      const selectedImageUri = result.assets[0].uri;
      const imageUri =
        Platform.OS === 'android'
          ? selectedImageUri
          : selectedImageUri.replace('file://', '');

      const formData = new FormData();
      formData.append('avatar', {
        uri: imageUri,
        name: 'photo.jpg',
        type: 'image/jpeg',
      });

      try {
        await axiosRequest({
          method: 'PUT',
          url: `/users/${user?._id}`,
          data: formData,
        });
        setForm(prev => ({ ...prev, avatar: selectedImageUri }));
        setOutsource(true);
        setBottomSheetVisible(false);
      } catch (error) {
        console.error('Error uploading image', error);
      }
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      mutate({ ...form });
      Toast.show({ title: t('edit-success'), type: ALERT_TYPE.SUCCESS });
    } catch (err) {
      Toast.show({ title: t('error-saving'), type: ALERT_TYPE.DANGER });
    } finally {
      setLoading(false);
    }
  };

  const toggleBottomSheet = () => {
    setBottomSheetVisible(prev => !prev);
  };

  return (
    <>
      {loading && <Loading />}
      <View style={{ flex: 1, backgroundColor: COLORS.lightWhite }}>
        <View style={styles.header}>
          <View style={{ alignItems: 'center' }}>
            <TouchableOpacity onPress={toggleBottomSheet}>
              <CustomAvatar
                avatar={form.avatar}
                fullName={form.fullName}
                size={120}
                outSource={outsource}
              />
              <MaterialIcons
                name="add-photo-alternate"
                size={SIZES.xLarge}
                color={COLORS.white}
                style={styles.icon}
              />
            </TouchableOpacity>
            <Text style={styles.headerText}>{data?.fullName ?? ''}</Text>
            <Text style={styles.subHeaderText}>{data?.jobTitle}</Text>
            <Text style={styles.subText}>{data?.jobDescription}</Text>
          </View>
        </View>

        <SafeAreaView style={{ flex: 1 }}>
          <KeyboardAwareScrollView style={styles.formContainer}>
            <CustomInput
              title={t('username')}
              value={form.fullName}
              handleChangeText={text => handleChange('fullName', text)}
              errorText={errors.fullName}
            />
            <CustomInput
              title={t('address')}
              value={form.address}
              handleChangeText={text => handleChange('address', text)}
            />
            <CustomInput
              title={t('phone')}
              value={form.phone}
              handleChangeText={text => handleChange('phone', text)}
              errorText={errors.phone}
            />
            <CustomInput
              title={t('email')}
              value={form.email}
              handleChangeText={text => handleChange('email', text)}
            />
          </KeyboardAwareScrollView>

          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveText}>{t('save')}</Text>
          </TouchableOpacity>
        </SafeAreaView>

        {bottomSheetVisible && (
          <BottomSheet offset={offset} toggleSheet={toggleBottomSheet}>
            <View style={styles.bottomSheet}>
              <TouchableOpacity
                onPress={handleImagePicker}
                style={styles.bottomSheetButton}
              >
                <Entypo name="images" size={24} color="black" />
                <Text>{t('choose-from-gallery')}</Text>
              </TouchableOpacity>
            </View>
          </BottomSheet>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: COLORS.primary,
    alignItems: 'center',
  },
  icon: {
    position: 'absolute',
    bottom: 0,
    right: 0,
  },
  headerText: {
    color: COLORS.white,
    fontWeight: '600',
    fontSize: 24,
  },
  subHeaderText: {
    color: COLORS.white,
    fontWeight: '600',
    fontSize: 18,
  },
  subText: {
    color: COLORS.gray,
    fontWeight: '300',
    fontSize: SIZES.medium,
    paddingBottom: 8,
  },
  formContainer: {
    paddingHorizontal: SIZES.small,
    marginTop: SIZES.xLarge,
  },
  saveButton: {
    borderRadius: 5,
    backgroundColor: COLORS.secondary,
    padding: SIZES.small,
  },
  saveText: {
    fontSize: SIZES.medium,
    color: COLORS.white,
    textAlign: 'center',
  },
  bottomSheet: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
  },
  bottomSheetButton: {
    alignItems: 'center',
  },
});

export default UserProfileScreen;
