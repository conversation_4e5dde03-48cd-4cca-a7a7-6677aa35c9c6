import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { View, Text, SafeAreaView, StyleSheet, Alert } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { MaterialCommunityIcons } from 'react-native-vector-icons';

import * as Haptics from 'expo-haptics';

import { COLORS, SIZES, globalStyles } from '@/Constants';
import { useAppDispatch } from '@/Redux/Store';
import { editUserDataThunk } from '@/Redux/Thunk/userThunk';

const TwoFactorAuthScreen = () => {
  const navigation = useNavigation<any>();
  const dispatch = useAppDispatch();

  const [code, setCode] = React.useState<string[]>([]);
  const [password, setPassword] = React.useState('');

  const [error, setError] = React.useState<string>('');
  const codeLength = Array(6).fill(0);

  const onNumberPress = (number: string) => {
    if (code.length === 6) {
      return;
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCode(old => [...old, number]);
  };

  const resetCode = () => {
    setCode([]);
    setError("Password Doesn't Match");
    OFFSET.value = withSequence(
      (OFFSET.value = withSequence(
        withTiming(-OFFSET.value, { duration: TIMING / 2 }), // Extract value from SharedValue
        withRepeat(
          withTiming(OFFSET.value + 50, { duration: TIMING }),
          4,
          true
        ), // Extract value
        withTiming(0, { duration: TIMING }) // No need to extract value here
      ))
    );
  };

  const onNumberBackSpacePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCode(code.slice(0, -1));
  };

  const OFFSET = useSharedValue(0);
  const TIMING = 80;

  const style = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: OFFSET.value }],
    };
  });

  React.useEffect(() => {
    if (code.length === 6) {
      if (!password) {
        setPassword(code.join(''));
        setError('');
        setCode([]);
      }
      // validate password
      else if (code.length === 6 && password === code.join('')) {
        if (code.join('') === password) {
          /*
            1- edit user data accessCode
            2- edit local storage
            3- redirect to page 
            4- set the auth always on in 
            */

          dispatch(editUserDataThunk({ accessCode: password }));

          Alert.alert('password the same');
          setPassword('');
          setCode([]);
          setError('');
          navigation.navigate('SettingsScreen');
        }
      } else {
        resetCode();
      }
      // if code is wrong

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [code]);

  return (
    <SafeAreaView style={globalStyles.SafeAreaView}>
      <View className="py-2 px-5">
        <Text className="text-black text-center text-xl  font-bold my-10">
          {password ? 'Enter The Same Password' : 'Enter Your Password'}
        </Text>

        {/* code view */}
        <Animated.View
          className="flex-row justify-center w-full  gap-4"
          style={style}
        >
          {codeLength.map((_, indx) => (
            <View
              key={indx}
              className={`w-5 h-5 rounded-full ${
                code[indx] ? 'bg-blue-500' : 'bg-gray-300 '
              }`}
            ></View>
          ))}
        </Animated.View>

        <Text className="text-red-500 text-center mt-2 font-bold">{error}</Text>

        {/* numbers view */}
        <View className="px-5  mt-10">
          <View className="flex-row   justify-between">
            {[1, 2, 3].map(number => (
              <TouchableOpacity
                style={styles.numberButton}
                key={number}
                onPress={() => onNumberPress(String(number))}
              >
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row   justify-between">
            {[4, 5, 6].map(number => (
              <TouchableOpacity
                style={styles.numberButton}
                key={number}
                onPress={() => onNumberPress(String(number))}
              >
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row   justify-between">
            {[7, 8, 9].map(number => (
              <TouchableOpacity
                style={styles.numberButton}
                key={number}
                onPress={() => onNumberPress(String(number))}
              >
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row   justify-between items-center ">
            <TouchableOpacity
              style={styles.numberButton}
              onPress={() => {
                setCode([]);
                setPassword('');
              }}
            >
              <MaterialCommunityIcons
                name="lock-reset"
                size={24}
                color="black"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.numberButton}
              onPress={() => onNumberPress(String(0))}
            >
              <Text style={styles.number}>0</Text>
            </TouchableOpacity>

            {code.length > 0 ? (
              <TouchableOpacity
                style={styles.numberButton}
                onPress={onNumberBackSpacePress}
              >
                <MaterialCommunityIcons name="backspace-outline" size={26} />
              </TouchableOpacity>
            ) : (
              <View className="min-w-[89px]" />
            )}
          </View>
        </View>
        {/* End oF Number View */}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  numberButton: {
    backgroundColor: '#F2F2F2',
    padding: 33,
    gap: 10,
    borderRadius: 10,
    marginBottom: 10,
  },
  number: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.black,
    textAlign: 'center',
  },
});

export default TwoFactorAuthScreen;
