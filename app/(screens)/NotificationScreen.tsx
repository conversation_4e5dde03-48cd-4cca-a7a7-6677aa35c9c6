import React, { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Text, View } from 'react-native';

import { FlashList } from '@shopify/flash-list';

import { useRouter } from 'expo-router';

import { axiosRequest } from '@/utils/useAxiosFetch';

import EmptyNotifications from '@/assets/Notifications.png';

import NotificationItem from '@/Components/NotificationItem/NotificationItem';
import { COLORS, SIZES } from '@/Constants';
import Loading from '@/Layouts/Loading/Loading';
import { notificationsActions } from '@/Redux/Slices/NotificationsSlice';
import { useAppDispatch, useAppSelector } from '@/Redux/Store';

export interface INotificationItem {
  createdAt: Date;
  active: boolean;
  from: {
    avatar: string;
    fullName: string;
    userName?: string;
    _id: string;
  };
  path: string;
  pathId: string;
  _id: string;
}

const EmptyListComponent = ({ t, loading }: { t: any; loading: boolean }) => {
  return !loading ? (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Image source={EmptyNotifications} />
      <Text style={{ fontSize: SIZES.large, color: COLORS.blue }}>
        {t('no-notifications')}
      </Text>
    </View>
  ) : null;
};

const NotificationScreen = () => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const router = useRouter();
  const { _id } = useAppSelector(s => s.user.data);
  const [loading, setLoading] = useState<boolean>(false);
  const [notifications, setNotifications] = useState<INotificationItem[]>([]);
  const [pagination, setPagination] = useState({
    skip: 0,
    limit: 10,
    total: 0,
  });

  const fetchNotifications = useCallback(() => {
    if (!_id) return;

    setLoading(true);
    axiosRequest({
      method: 'GET',
      url: `/notifications?skip=${pagination.skip}&limit=${pagination.limit}`,
    })
      .then(response => {
        if (response.data) {
          const newNotifications = response.data.data.filter(
            (newItem: INotificationItem) =>
              !notifications.some(
                (existingItem: INotificationItem) =>
                  existingItem._id === newItem._id
              )
          );
          setNotifications(prev => [...prev, ...newNotifications]);
          setPagination(prev => ({
            ...prev,
            total: response.data.length,
          }));
          dispatch(notificationsActions.incrementOrDecrement(+1));
        }
      })
      .catch(err => console.error(err))
      .finally(() => setLoading(false));
  }, [pagination.skip, pagination.limit, _id, notifications, dispatch]);

  const renderNotificationItem = useCallback(
    ({ item }: { item: INotificationItem }) => (
      <NotificationItem
        {...item}
        onPress={() =>
          router.push({
            pathname: '(screens)/ChatScreen',
            params: { _id: item.pathId },
          })
        }
      />
    ),
    [router]
  );

  const loadMoreNotifications = () => {
    if (notifications.length === 0 || notifications.length >= pagination.total)
      return;

    setPagination(prev => ({
      ...prev,
      skip: prev.skip + prev.limit,
    }));
  };

  useEffect(() => {
    fetchNotifications();
  }, [pagination.skip]);

  return (
    <View style={{ flex: 1, backgroundColor: COLORS.white }}>
      {loading && <Loading />}
      <FlashList
        onRefresh={fetchNotifications}
        refreshing={loading}
        alwaysBounceVertical={true}
        bounces={true}
        contentContainerStyle={{ padding: SIZES.xSmall }}
        estimatedItemSize={132}
        data={notifications}
        ListEmptyComponent={() => (
          <EmptyListComponent t={t} loading={loading} />
        )}
        keyExtractor={(item, index) =>
          item._id ? item._id : `notification-${index}`
        }
        renderItem={renderNotificationItem}
        onEndReached={loadMoreNotifications}
        onEndReachedThreshold={0.5}
      />
    </View>
  );
};

export default NotificationScreen;
