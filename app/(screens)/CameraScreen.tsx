import React from 'react';
import { View, Image, Alert } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { CameraType, FlashMode, Camera } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';

import CustomButton from '@/Components/CustomButton/CustomButton';
import { COLORS } from '@/Constants';

const CameraScreen = ({ navigation }) => {
  const [type, setType] = React.useState(CameraType.back);
  const [flash, setFlash] = React.useState(FlashMode.off);
  const [image, setImage] = React.useState(null);
  const cameraRef = React.useRef<Camera>(null);
  const [permissionResponse, requestPermission] = MediaLibrary.usePermissions();

  const toggleFlash = () => {
    if (flash['off'] === 0) {
      // console.log("yes");
    } else {
      //   setFlash(FlashMode.off);
      // }
      return;
    }
  };
  const takeAPicture = async () => {
    if (cameraRef) {
      try {
        const data = await cameraRef.current.takePictureAsync();
        setImage(data.uri);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const saveImage = async () => {
    if (image) {
      try {
        const SavedImage = await MediaLibrary.createAssetAsync(image);
        Alert.alert('Image Saved');
        setImage(null);
      } catch (error) {
        if (MediaLibrary.PermissionStatus.DENIED) {
          requestPermission()
            .then(() => {
              // console.log(permissionResponse.status)
            })
            .catch(err => console.log('Media library permission error'));
        }
        console.log('Error Saving Image', error);
      }
    }
  };

  const toggleCamera = () => {
    if (type === CameraType.back) {
      setType(CameraType.front);
    } else {
      setType(CameraType.back);
    }
    return;
  };

  return (
    <View className="flex-1">
      {!image ? (
        <View className="flex-1">
          <View className="absolute top-10 left-5 flex-1 items-center justify-center  h-10 w-10 rounded-full bg-white z-30">
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Ionicons name="close" size={24} color={COLORS.black} />
            </TouchableOpacity>
          </View>
          <Camera
            className="flex-1 aspect-square"
            type={type}
            flashMode={flash}
            ref={cameraRef}
          />

          <View className="w-full bg-white pb-10 pt-4 px-6 flex-row  items-center  justify-between  absolute bottom-0">
            <TouchableOpacity onPress={() => toggleFlash()}>
              {flash === 'on' ? (
                <Ionicons name="flash" size={30} color="black" />
              ) : (
                <Ionicons name="flash-off" size={30} color="black" />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              className="w-20 h-20 bg-blue-100    rounded-full border-blue-500 border-[10px]"
              onPress={takeAPicture}
            />
            <TouchableOpacity onPress={toggleCamera}>
              <MaterialIcons name="flip-camera-ios" size={40} color="black" />
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <>
          <Image source={{ uri: image }} className="w-screen h-screen" />

          <View className="w-full bg-white pb-10 pt-4 px-6 flex-row  items-center  justify-between  absolute bottom-0">
            <CustomButton
              onPress={() => setImage(null)}
              title="Go back"
              bgColor={COLORS.white}
              textColor={COLORS.primary}
            />
            <CustomButton
              onPress={() => saveImage()}
              title="Save"
              bgColor={COLORS.white}
              textColor={COLORS.primary}
            />
          </View>
        </>
      )}
    </View>
  );
};

export default CameraScreen;
