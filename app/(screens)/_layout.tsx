import React from 'react';

import { COLORS } from '@/Constants';
import CustomHeader from '@/Layouts/CustomHeader/CustomHeader';
import { t } from 'i18next';

import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

interface IHeaderOptions {
  route?: any;
  navigation?: any;
  headerRight?: () => any;
  headerLeft?: () => any;
  title?: string;
}

const headerOptions = ({
  navigation,
  headerRight,
  headerLeft,
  title,
}: IHeaderOptions) => ({
  header: () => (
    <CustomHeader
      title={title ?? ''}
      showBackButton={navigation.canGoBack()} // Show back button if there is something to go back to
      onBackPress={() => navigation.goBack()} // Handle back button press
      headerRight={headerRight} // Pass the custom headerRight if provided
      headerLeft={headerLeft} // Pass the custom headerRight if provided
    />
  ),
  headerStyle: {
    backgroundColor: COLORS.primary,
  },
});

const _layout = () => {
  return (
    <>
      <StatusBar style="light" />
      <Stack>
        {/* Pass headerOptions for each screen, allowing headerRight for some */}
        <Stack.Screen
          name="AttendanceTablesScreen"
          options={({ route, navigation }) =>
            headerOptions({ route: route, navigation, title: t('time-table') })
          }
        />
        <Stack.Screen name="ChatScreen" options={{ headerShown: false }} />
        <Stack.Screen
          name="(user-requests)/LeaveRequestScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation, title: t('user-requests') })
          }
        />
        <Stack.Screen
          name="(user-requests)/UserAllRequestsScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation, title: t('user-requests') })
          }
        />
        <Stack.Screen
          name="LocationsScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation, title: t('locations') })
          }
        />
        <Stack.Screen
          name="MapScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation })
          }
        />

        <Stack.Screen
          name="NotificationScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation, title: t('notifications') })
          }
        />
        <Stack.Screen
          name="SettingsScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation, title: t('settings') })
          }
        />
        <Stack.Screen
          name="TermsAndConditionsScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation })
          }
        />
        <Stack.Screen
          name="TwoFactorAuthScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation })
          }
        />
        <Stack.Screen
          name="UserProfileScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation })
          }
        />
        <Stack.Screen
          name="CameraScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation })
          }
        />
        <Stack.Screen
          name="WhiteScreen"
          options={({ route, navigation }) =>
            headerOptions({ route, navigation })
          }
        />
      </Stack>
    </>
  );
};

export default _layout;
