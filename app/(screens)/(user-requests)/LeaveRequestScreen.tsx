import { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Text,
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SelectList } from 'react-native-dropdown-select-list';
import { Button, Modal, Portal } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import CustomInput from '@/Components/CustomInputs/CustomInput';
import CustomDateTimePicker from '@/Components/DateTimePicker/DatePicker';
import { COLORS, SIZES, s } from '@/Constants';
import i18n from '@/i18n';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { router } from 'expo-router';

import usePostUserRequest from '@/Hooks/userRequests/usePostUserRequest';

import { customToast } from '@/utils/toast';

import { LEAVE_TYPES } from '@/types/Index';

const defaultValues = {
  startDate: new Date(),
  endDate: new Date(),
  startTime: new Date(),
  endTime: new Date(),
  type: LEAVE_TYPES.ABSENT,
  description: '',
};

const formSchema = yup.object({
  startDate: yup.date().required('Start Date is required'),
  endDate: yup
    .date()
    .min(yup.ref('startDate'), 'End Date cannot be before Start Date')
    .required('End Date is required'),
  startTime: yup.date().notRequired().nullable(),
  endTime: yup
    .date()
    .notRequired()
    .nullable()
    .when('startTime', (startTime, schema) =>
      startTime
        ? schema.min(
            yup.ref('startTime'),
            'End Time cannot be before Start Time'
          )
        : schema
    ),
  type: yup
    .mixed<LEAVE_TYPES>()
    .oneOf(Object.values(LEAVE_TYPES) as LEAVE_TYPES[], 'Invalid request type')
    .required('Request type is required'),
  description: yup
    .string()
    .when('type', {
      is: LEAVE_TYPES.OTHER,
      then: schema =>
        schema.required('Description is required for "Other" type'),
      otherwise: schema => schema.notRequired(),
    })
    .nullable(),
});

export type IPostUserRequestPayload = yup.InferType<typeof formSchema>;

const LeaveRequestScreen = () => {
  const inset = useSafeAreaInsets();

  const [errorsModal, setErrorsModal] = useState(false);
  const [confirmModal, setConfirmModal] = useState(false);

  const {
    control,
    formState: { errors },
    clearErrors,
    watch,
    handleSubmit,
  } = useForm<IPostUserRequestPayload>({
    resolver: yupResolver(formSchema),
    defaultValues: defaultValues,
  });

  const requestType = watch('type');

  const RequestTypeOptions = useMemo(
    () => [
      { value: i18n.t('leave'), key: LEAVE_TYPES.LEAVE },
      { value: i18n.t('absent'), key: LEAVE_TYPES.ABSENT },
      { value: i18n.t('other'), key: LEAVE_TYPES.OTHER },
    ],
    []
  );

  // Submit api
  const { mutate, isPending } = usePostUserRequest();

  const onSubmit = (form: IPostUserRequestPayload) => {
    setConfirmModal(true); // Show confirm modal first
    // We'll call mutate(form) only if user confirms
    pendingForm.current = form; // Store form data temporarily
  };

  // Store form data between modals
  const pendingForm = useRef<IPostUserRequestPayload | null>(null);

  // Call this when user confirms
  const handleConfirmPost = () => {
    if (pendingForm.current) {
      mutate(pendingForm.current, {
        onSuccess: () => {
          customToast(i18n.t('Sent Successfully'), 'success');
        },
      });
      pendingForm.current = null;
    }
    setConfirmModal(false);
  };

  const handleCancelPost = () => {
    setConfirmModal(false);
    pendingForm.current = null;
  };

  const handleCloseModal = () => {
    setErrorsModal(false);
    clearErrors();
  };

  useEffect(() => {
    if (Object.keys(errors).length > 0) setErrorsModal(true);
  }, [errors]);
  return (
    <>
      {/* Errors Modal */}
      <Portal>
        <Modal
          style={styles.modalContainer}
          visible={errorsModal}
          onDismiss={handleCloseModal}
          contentContainerStyle={styles.modalContent}
        >
          <Text style={styles.modalTitle}>{i18n.t('Validation Errors')}</Text>
          {Object.entries(errors).map(([key, err], idx) =>
            err?.message ? (
              <Text key={key} style={styles.modalErrorText}>
                • {err.message}
              </Text>
            ) : null
          )}
          <Button
            mode="contained"
            onPress={handleCloseModal}
            style={styles.modalButton}
            buttonColor={COLORS.primary}
            textColor={COLORS.white}
          >
            {i18n.t('Close')}
          </Button>
        </Modal>
      </Portal>

      {/* Confirm Modal  */}

      <Portal>
        <Modal
          style={styles.modalContainer}
          visible={confirmModal}
          onDismiss={handleCancelPost}
          contentContainerStyle={styles.modalContent}
        >
          <Text style={styles.modalTitle}>
            {i18n.t('Are you sure you want to post?')}
          </Text>
          <View style={{ flexDirection: 'row', marginTop: 24, gap: 16 }}>
            <Button
              mode="contained"
              onPress={handleConfirmPost}
              style={[styles.modalButton, { flex: 1 }]}
              buttonColor={COLORS.primary}
              textColor={COLORS.white}
              loading={isPending}
            >
              {i18n.t('Yes')}
            </Button>
            <Button
              mode="outlined"
              onPress={handleCancelPost}
              style={[styles.modalButton, { flex: 1 }]}
              textColor={COLORS.primary}
            >
              {i18n.t('No')}
            </Button>
          </View>
        </Modal>
      </Portal>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        style={{ flex: 1 }}
      >
        <ScrollView
          keyboardDismissMode="on-drag"
          showsVerticalScrollIndicator={false}
          style={styles.container}
          contentContainerStyle={{ paddingBottom: 100, flexGrow: 1 }}
        >
          <Controller
            control={control}
            name="type"
            render={({ field }) => (
              <View style={styles.inputContainer}>
                <SelectList
                  defaultOption={{
                    key: field.value,
                    value: i18n.t(field.value),
                  }}
                  data={RequestTypeOptions}
                  setSelected={(selected: any) => field.onChange(selected)}
                  save="key"
                  search={false}
                  placeholder={i18n.t('select-request-type')}
                  boxStyles={{
                    ...styles.selectBox,
                    borderColor: errors.type ? COLORS.danger : COLORS.gray2,
                  }}
                  dropdownStyles={styles.dropdown}
                  inputStyles={styles.selectInput}
                  dropdownTextStyles={styles.dropdownText}
                />
                {errors.type && (
                  <Text style={styles.errorText}>{errors.type.message}</Text>
                )}
              </View>
            )}
          />
          {/* Row  date picker  */}

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: s(4),
            }}
          >
            <Controller
              control={control}
              name="startDate"
              render={item => (
                <View style={{ flex: 1 }}>
                  <CustomDateTimePicker
                    title="StartDate"
                    value={item.field.value}
                    onDateChange={item.field.onChange}
                  />
                </View>
              )}
            />

            <Controller
              control={control}
              name="endDate"
              render={item => (
                <View style={{ flex: 1 }}>
                  <CustomDateTimePicker
                    title="endDate"
                    value={item.field.value}
                    onDateChange={item.field.onChange}
                  />
                </View>
              )}
            />
          </View>
          {/* Row timer picker */}
          <View
            style={[
              styles.row,
              requestType !== LEAVE_TYPES.LEAVE && styles.hidden,
            ]}
          >
            <View style={styles.datePicker}>
              <Controller
                control={control}
                name="startTime"
                render={({ field }) => (
                  <CustomDateTimePicker
                    title={i18n.t('start-time')}
                    value={field.value}
                    onDateChange={field.onChange}
                    mode="time"
                    error={errors.startTime?.message}
                  />
                )}
              />
            </View>
            <View style={styles.datePicker}>
              <Controller
                control={control}
                name="endTime"
                render={({ field }) => (
                  <CustomDateTimePicker
                    title={i18n.t('end-time')}
                    value={field.value}
                    onDateChange={field.onChange}
                    mode="time"
                    error={errors.endTime?.message}
                  />
                )}
              />
            </View>
          </View>

          {/* On selecting  Other => show  reason input */}

          <Controller
            control={control}
            name="description"
            render={({ field }) => (
              <CustomInput
                title={i18n.t('Description')}
                placeholder={`${i18n.t('write-your-reason')}...`}
                value={field.value || ''}
                handleChangeText={field.onChange}
                errorText={errors.description?.message}
                multiline={true}
                style={{ height: 120 }}
                smartInsertDelete={true}
              />
            )}
          />
          <TouchableOpacity
            onPress={() =>
              router.push('/(user-requests)/UserAllRequestsScreen')
            }
          >
            <Text
              style={{
                color: COLORS.primary,
                fontSize: s(15),
                fontWeight: 'semibold',
              }}
            >
              {i18n.t('All-requests')}
            </Text>
          </TouchableOpacity>
        </ScrollView>
        <View style={[styles.fixedButtonContainer, { bottom: inset.bottom }]}>
          <Button
            buttonColor={COLORS.primary}
            textColor={COLORS.white}
            style={styles.submitButton}
            loading={isPending}
            onPress={handleSubmit(onSubmit)}
          >
            {i18n.t('Submit')}
          </Button>
        </View>
      </KeyboardAvoidingView>
    </>
  );
};

export default LeaveRequestScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.lightWhite,
    paddingHorizontal: SIZES.medium,
    flex: 1,
    paddingTop: s(20),
  },
  card: {
    marginTop: SIZES.medium,
    borderRadius: SIZES.small,
    elevation: 3,
  },
  title: {
    textAlign: 'center',
    color: COLORS.primary,
    marginBottom: SIZES.large,
  },
  inputContainer: {
    marginBottom: SIZES.medium,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.medium,
  },
  datePicker: {
    flex: 1,
    marginHorizontal: SIZES.xSmall,
  },
  selectBox: {
    borderRadius: SIZES.small,
    borderColor: COLORS.gray2,
    height: 55,
  },
  selectInput: {
    color: COLORS.primary,
  },
  dropdown: {
    borderColor: COLORS.gray2,
  },
  dropdownText: {
    color: COLORS.black,
    fontSize: SIZES.small,
  },
  errorText: {
    color: COLORS.danger,
    marginTop: SIZES.xSmall,
    fontSize: SIZES.small,
  },

  submitButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  listTitle: {
    color: COLORS.secondary,
    fontSize: SIZES.large,
    fontWeight: 'bold',
    marginVertical: SIZES.medium,
    paddingHorizontal: SIZES.small,
  },
  hidden: {
    display: 'none',
  },
  requestItemCard: {
    marginVertical: SIZES.xSmall,
    marginHorizontal: SIZES.small,
    borderRadius: SIZES.small,
    elevation: 2,
  },
  requestItemTitle: {
    textTransform: 'capitalize',
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SIZES.xxLarge,
  },
  fixedButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,

    backgroundColor: COLORS.lightWhite,
    padding: SIZES.medium,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray2,
  },
  submitButton: {
    borderRadius: SIZES.small,
    paddingVertical: SIZES.xSmall,
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.medium,
    padding: SIZES.large,
    minWidth: 280,
    alignItems: 'center',
    shadowColor: COLORS.gray2,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  modalTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SIZES.medium,
    textAlign: 'center',
  },
  modalErrorText: {
    color: COLORS.danger,
    fontSize: SIZES.medium,
    marginBottom: SIZES.xSmall,
    textAlign: 'left',
    alignSelf: 'flex-start',
  },
  modalButton: {
    marginTop: SIZES.large,
    alignSelf: 'stretch',
    borderRadius: SIZES.small,
  },
});
