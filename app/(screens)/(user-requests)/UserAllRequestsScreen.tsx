import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

import RequestCard from '@/Components/RequestCard';
import { COLORS, SIZES } from '@/Constants';
import Loading from '@/Layouts/Loading/Loading';
import i18n from '@/i18n';
import { FlashList } from '@shopify/flash-list';

import useGetAllUserRequests from '@/Hooks/userRequests/useGetAllUserRequests';

const UserAllRequestsScreen = () => {
  const { data, isPending } = useGetAllUserRequests();

  if (isPending) {
    return <Loading />;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{i18n.t('allRequests')}</Text>
      <FlashList
        estimatedItemSize={136}
        data={data?.data}
        keyExtractor={item => item._id}
        renderItem={({ item }) => <RequestCard item={item} />}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>{i18n.t('noRequests')}</Text>
          </View>
        )}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

export default UserAllRequestsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  title: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.primary,
    paddingHorizontal: SIZES.medium,
    paddingTop: SIZES.large,
    paddingBottom: SIZES.small,
  },
  listContainer: {
    paddingBottom: SIZES.large,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SIZES.xxLarge,
  },
  emptyText: {
    fontSize: SIZES.large,
    color: COLORS.grayText,
  },
});
