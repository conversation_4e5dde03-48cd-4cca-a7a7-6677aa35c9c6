import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, ScrollView } from 'react-native';
import { Divider, Switch } from 'react-native-paper';

import { COLORS } from '@/Constants';
import { SCREEN_WIDTH } from '@/Constants/Theme';
import { useAppDispatch, useAppSelector } from '@/Redux/Store';

import useLanguage from '@/Hooks/useLanguage';

const Title = (props: { title: string }) => {
  return (
    <View className="px-4  my-2">
      <Text className="text-2xl text-end" style={{ color: COLORS.blue }}>
        {props.title}
      </Text>
    </View>
  );
};

type ISwitchTypes = {
  title?: string;
  value: boolean;
  onValueChange: any;
  textValue?: string;
  isArabic?: boolean;
};
const SwitchWithTitle = (props: ISwitchTypes) => {
  return (
    <View
      className={` items-center gap-3 ${
        props.isArabic ? 'flex-row-reverse' : 'flex-row'
      }`}
    >
      <View>
        <Text className="font-semibold text-xl">{props.title}</Text>
        <Text className="text-secondary text-lg">{props?.textValue ?? ''}</Text>
      </View>
      <Switch
        color={COLORS.secondary}
        value={props.value}
        onValueChange={props.onValueChange}
      />
    </View>
  );
};

const SettingsScreen = () => {
  const { isArabic } = useLanguage();
  const { t, i18n } = useTranslation();
  const navigation = useNavigation();
  const userAccessCode = useAppSelector(state => state.user.data?.accessCode);

  const [settings, setSettings] = useState({
    isArabic: false,
    fastAccess: false,
    accessCode: false,
  });

  // Load settings from AsyncStorage when the component mounts
  useEffect(() => {
    AsyncStorage.getItem('app-settings')
      .then(response => {
        if (response) {
          setSettings(JSON.parse(response));
        }
      })
      .catch(err => {
        console.log('Settings screen, failed to get LocalStorage', err);
      });
  }, []);

  // Save settings to AsyncStorage
  const saveSettings = (newSettings: {
    isArabic: boolean;
    fastAccess: boolean;
    accessCode: boolean;
  }) => {
    AsyncStorage.setItem('app-settings', JSON.stringify(newSettings)).catch(
      err => {
        console.log('Failed to save settings to LocalStorage', err);
      }
    );
  };

  // Toggle language setting
  const toggleLanguage = useCallback(() => {
    setSettings(prev => {
      const updatedSettings = { ...prev, isArabic: !prev.isArabic };
      const isArabic = !prev.isArabic ? 'ar' : 'en';
      i18n.changeLanguage(isArabic).then(() => {
        saveSettings(updatedSettings);
        // Refresh the app to apply the language change
        navigation.reset({
          index: 0,
          routes: [{ name: navigation?.getState()?.routes[0].name as never }],
        });
      });
      return updatedSettings;
    });
  }, [i18n, navigation]);
  // Toggle fast access setting
  const toggleFastAccess = () => {
    if (settings.accessCode) {
      // dispatch(editUserDataThunk({accessCode: ""}));
      setSettings(prev => {
        const updatedSettings = { ...prev, fastAccess: !prev.fastAccess };
        saveSettings(updatedSettings);
        return updatedSettings;
      });
    } else {
      navigation.navigate('TwoFactorAuthScreen' as never);
    }
  };

  // Toggle access code setting based on userAccessCode
  useEffect(() => {
    setSettings(prev => {
      const updatedSettings = {
        ...prev,
        accessCode: String(userAccessCode ?? '').length > 0,
      };
      saveSettings(updatedSettings);
      return updatedSettings;
    });
  }, [userAccessCode]);

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        style={{ flex: 1, alignSelf: isArabic ? 'flex-end' : 'flex-start' }}
      >
        {/* <Title title={t("Settings")} /> */}
        <Text className="text-center text-2xl my-4 text-secondary font-bold">
          {' '}
          {t('settings')}{' '}
        </Text>
        <View
          className="px-4"
          style={{ alignSelf: isArabic ? 'flex-end' : 'flex-start' }}
        >
          <Text className="text-2xl" style={{ color: COLORS.blue }}>
            {t('application-language')}
          </Text>

          <SwitchWithTitle
            isArabic={isArabic}
            onValueChange={toggleLanguage}
            textValue={settings.isArabic ? 'العربية' : 'English'}
            value={settings.isArabic}
            title={t('language')}
          />
        </View>
        <Divider style={{ width: SCREEN_WIDTH - 3, marginVertical: 10 }} />
        <View className="p-4">
          <Text className="text-2xl" style={{ color: COLORS.blue }}>
            {t('fast-access')}
          </Text>

          <SwitchWithTitle
            isArabic={isArabic}
            onValueChange={toggleFastAccess}
            value={settings.accessCode}
            title={t('face-id')}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default SettingsScreen;
