import React from 'react';
import { View, Text, SafeAreaView } from 'react-native';

import { COLORS, globalStyles } from '@/Constants';

const TermsAndConditionsScreen = () => {
  return (
    <SafeAreaView style={globalStyles.SafeAreaView}>
      <View className="px-5 pt-5">
        <Text className="text-black mb-6">
          Please Read The Terms of services, Carefully before using our
          application opereated by us
        </Text>

        <Text
          className="text-2xl font-bold mb-7"
          style={{ color: COLORS.blue }}
        >
          Conditions of Users
        </Text>
        <Text className="text-xl">
          It is a long established fact that a reader will be distracted by the
          readable content of a page when looking at its layout. The point of
          using Lorem Ipsum is that it has a more-or-less normal distribution of
          letters, as opposed to using 'Content here, content here', making it
          look like readable English. Many desktop publishing packages and web
          page editors now use Lorem Ipsum as their default model text, and a
          search for 'lorem ipsum' will uncover many web sites still in their
          infancy. Various versions have evolved over the years, sometimes by
          accident, sometimes on purpose (injected humour and the like).
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default TermsAndConditionsScreen;
