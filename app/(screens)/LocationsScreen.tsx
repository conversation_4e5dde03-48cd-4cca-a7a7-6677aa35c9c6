import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';

import { FlashList } from '@shopify/flash-list';
import { ILocation } from 'Redux/Slices/locationSlice';

import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import UseRefresh from '@/utils/useRefresh';

import { useAppSelector } from '@/Redux/Store';

const LocationsScreen = () => {
  const locations = useAppSelector(state => state.user.data?.locations);
  const router = useRouter();
  return (
    <UseRefresh callback={() => {}}>
      <ScrollView>
        <View className="py-2 h-[100vh] w-full bg-white  ">
          <FlashList
            data={locations as ILocation[]}
            bounces={true}
            estimatedItemSize={200}
            keyExtractor={item => item?._id ?? ''}
            renderItem={({ item }) => {
              return (
                <TouchableOpacity
                  onPress={() =>
                    router.push({ pathname: 'MapScreen', params: item as any })
                  }
                  className="w-full mb-3 p-3 border-b border-b-gray-200"
                >
                  <Text className="flex-row gap-2">
                    {item.title}
                    <Text>
                      <Ionicons name="location" size={32} color={'red'} />
                    </Text>
                  </Text>
                  <Text>{item.description}</Text>
                </TouchableOpacity>
              );
            }}
          />
        </View>
      </ScrollView>
    </UseRefresh>
  );
};

export default LocationsScreen;
