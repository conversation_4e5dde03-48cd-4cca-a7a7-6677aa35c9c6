import React, { memo, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  View,
  TextInput,
} from 'react-native';
import {
  Appbar,
  Avatar,
  Button,
  IconButton,
  Modal,
  Portal,
} from 'react-native-paper';

import { FlashList } from '@shopify/flash-list';
import CustomAvatar from 'Components/CustomAvatar';

import Ionicons from '@expo/vector-icons/Ionicons';
import { useLocalSearchParams } from 'expo-router';

import { axiosRequest } from '@/utils/useAxiosFetch';
import { connectIo, socket } from '@/utils/useSocket';

import ChatBubble from '@/Components/ChatBubble';
import { COLORS, SIZES } from '@/Constants';
import { useAppSelector } from '@/Redux/Store';
import { socketEventsList } from '@/SocketEvents';
import { IMAGE_URL } from '@/config';

export interface IMessage {
  body: string;
  createdAt: Date;
  modified: boolean;
  priority: 'heigh' | 'medium' | 'normal';
  receivers: { _id: string; fullName: string; phone: string; avatar: string }[];
  replays: {
    createdAt: Date;
    modified: false;
    replay: string;
    sender: {
      _id: string;
      avatar: string;
      fullName: string;
      userName?: string;
    };
    senderData: { _id: string; avatar: string; fullName: string }[];
    role: 'admin' | 'employee';
    ticket: string;
    _id: string;
  }[];
  status: 'pending' | 'rejected';
  sender: {
    address: string;
    userName: string;
    _id: string;
    email: string;
    role: 'admin' | 'employee';
  };
  title: string;
  _id: string;
}

interface ISocketMsg {
  createdAt: Date;
  modified: boolean;
  replay: string;
  sender: {
    _id: string;
    fullName: string;
    userName?: string;
    avatar?: string;
  };
  ticket: string;
  title: String;
  updatedAt: Date;
  _id: string;
}
const { width } = Dimensions.get('screen');

const ChatScreen = () => {
  const { _id } = useLocalSearchParams();
  const { t } = useTranslation();
  const { data: userData } = useAppSelector(state => state?.user);

  const [loading, setLoading] = useState<boolean>(false);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  const [modal, setModal] = useState<boolean>(false);

  const [pagination, setPagination] = useState({
    skip: 0,
    limit: 10,
  });

  const [chats, setChats] = useState<IMessage | undefined>(undefined);
  const [message, setMessage] = useState<string>('');

  const listEmptyComponent = memo(() => {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Ionicons
          name="chatbubble-ellipses-outline"
          size={SIZES.xxLarge}
          color={COLORS.primary}
        />
        <Text style={{ textAlign: 'center', fontSize: 24 }}>
          {t('no-replays')}
        </Text>
      </View>
    );
  });

  const handleRefresh = useCallback(() => {
    setLoading(true);
    axiosRequest({
      method: 'GET',
      url: `/tickets/${_id}?skip=${pagination.skip}&limit=${pagination.limit}`,
    })
      .then(response => {
        setChats(response.data);
      })
      .catch(err => {
        console.log('Get Ticket By ID Error', err);
      })
      .finally(() => setLoading(false));
  }, []);

  const handleSubmit = useCallback(async () => {
    if (message?.trim().length === 0 || submitLoading) {
      return;
    }

    try {
      setSubmitLoading(true);
      if (!socket.connected) {
        connectIo();
      }

      if (socket) {
        socket.emit(
          socketEventsList.replays.create.emit,
          {
            replay: message,
            ticket: _id,
          },
          (data: { success: boolean }) => {
            if (data.success) {
              console.log('Message sent');
              setChats((old: any) => ({
                ...old,
                replays: [
                  ...old.replays,
                  {
                    replay: message,
                    createdAt: Date.now(),
                    sender: {
                      _id: userData._id,
                      fullName: userData.fullName,
                      userName: userData.fullName,
                      avatar: userData.avatar,
                    },
                  },
                ],
              }));
              setMessage('');
            }
          }
        );
      }
    } catch (error) {
      console.log('Send Replay Error', error);
    } finally {
      setSubmitLoading(false);
    }
  }, [socket, message, submitLoading]);

  useEffect(() => {
    const handleReceiveReplay = (message: ISocketMsg) => {
      setChats(prev => {
        const isDuplicate = prev?.replays?.some(
          (replay: { _id: string }) => replay._id === message._id
        );

        if (isDuplicate) {
          return prev; // Return previous state if duplicate is found
        }

        return {
          ...prev,
          replays: [
            ...prev?.replays,
            {
              ...message,
            },
          ],
        };
      });
    };

    if (socket) {
      socket.on(socketEventsList.replays.create.on, handleReceiveReplay);
    }

    return () => {
      if (socket) {
        socket.off(socketEventsList.replays.create.on, handleReceiveReplay);
      }
    };
  }, [socket]);

  // Call Tickets
  useEffect(() => {
    handleRefresh();
  }, [_id]);

  return (
    <View className="flex-1">
      {/* Chat header */}
      <View className="bg-primary py-1.5 ">
        <Appbar.Header style={styles.chatHeader} mode="center-aligned">
          <View style={styles.userContainer}>
            <CustomAvatar
              fullName={userData?.fullName}
              avatar={userData?.avatar}
            />

            <View>
              <Text style={styles.userTitle}>{userData?.fullName}</Text>
              <Text style={{ color: COLORS.grayText }}>
                {userData?.jobTitle}
              </Text>
            </View>
          </View>
          <Appbar.Action
            icon={'dots-vertical'}
            color={COLORS.white}
            onPress={() => setModal(true)}
          />
        </Appbar.Header>
      </View>

      <Portal>
        <Modal
          style={{ flex: 1 }}
          onDismiss={() => setModal(false)}
          visible={modal}
        >
          <View style={styles.modalContainer}>
            <Text
              style={{
                textAlign: 'center',
                color: COLORS.black,
                fontSize: SIZES.large,
              }}
            >
              {t('ticket-actions')}
            </Text>
            <Button
              buttonColor={COLORS.primary}
              textColor={COLORS.white}
              style={styles.actionButtonsStyles}
            >
              {t('accept')}
            </Button>
            <Button
              buttonColor={COLORS.danger}
              textColor={COLORS.white}
              style={styles.actionButtonsStyles}
            >
              {t('reject')}
            </Button>
          </View>
        </Modal>
      </Portal>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={20}
        style={{ flex: 1 }}
      >
        <FlashList
          onRefresh={handleRefresh}
          refreshing={loading}
          scrollsToTop={true}
          ListEmptyComponent={listEmptyComponent}
          data={chats?.replays ?? []}
          keyExtractor={(item, index) =>
            `${item._id ?? Math.floor(Math.random() * 1000)}_${index}`
          }
          renderItem={({ item }) => (
            <ChatBubble
              {...item}
              isCurrentUser={item?.sender?._id === userData?._id}
              date={new Date(item?.createdAt).toLocaleString()}
              senderName={
                (item?.sender?.fullName || item?.sender?.userName) ?? 'Admin'
              }
              avatar={
                item?.sender?.avatar
                  ? `${IMAGE_URL}${item?.sender?.avatar}`
                  : ''
              }
            />
          )}
          contentContainerStyle={styles.listContainerStyles}
          estimatedItemSize={135}
        />
        <View style={{ backgroundColor: COLORS.white, padding: SIZES.xSmall }}>
          <View style={styles.inputContainer}>
            <TextInput
              placeholder={t('message')}
              style={styles.input}
              value={message}
              onChangeText={e => setMessage(e)}
            />
            <IconButton
              onPress={handleSubmit}
              loading={submitLoading}
              containerColor={COLORS.blue}
              iconColor={COLORS.white}
              mode="contained"
              icon="send-circle"
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  chatHeader: {
    backgroundColor: COLORS.primary,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userContainer: {
    flexDirection: 'row',
    paddingBottom: SIZES.small,
    gap: SIZES.small,
  },
  userTitle: {
    fontWeight: 'semibold',
    fontSize: SIZES.xLarge,
    color: COLORS.white,
  },

  sendButton: {
    margin: SIZES.medium,
    borderRadius: SIZES.small,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.xSmall,
    borderWidth: 1,
    borderColor: COLORS.secondary,
    borderRadius: SIZES.xSmall,
    width: width - 30,
    marginHorizontal: 'auto',
  },
  input: {
    flex: 1,
    paddingHorizontal: SIZES.xSmall,
    color: COLORS.black,
    backgroundColor: COLORS.white,
  },
  listContainerStyles: {
    paddingVertical: SIZES.small,
    backgroundColor: '#eee',
    padding: SIZES.xSmall,
  },
  modalContainer: {
    gap: 10,
    backgroundColor: COLORS.white,
    width: '80%',
    alignSelf: 'center',
    borderRadius: SIZES.small,
    padding: SIZES.medium,
  },
  actionButtonsStyles: {
    borderRadius: 5,
  },
});

export default ChatScreen;
