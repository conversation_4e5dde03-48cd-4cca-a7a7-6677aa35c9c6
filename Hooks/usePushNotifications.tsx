import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';
import { Alert, Platform } from 'react-native';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';

import axios from 'axios';

import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';

import { axiosRequest } from 'utils/useAxiosFetch';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldShowAlert: true,
    shouldSetBadge: false,
  }),
});

export interface pushNotificationState {
  expoPushToken?: string;
  notification: Notifications.Notification;
}

const usePushNotifications = () => {
  const [expoPushToken, setExpoPushToken] = React.useState<string | undefined>(
    undefined
  );
  const [notification, setNotification] = React.useState<
    Notifications.Notification | undefined
  >(undefined);

  const notificationListener = React.useRef<Notifications.Subscription>();
  const responseListener = React.useRef<Notifications.Subscription>();

  const sendPushNotification = React.useCallback(
    async ({
      title,
      body,
      data,
    }: {
      title: string;
      body: string;
      data: any;
    }) => {
      if (!expoPushToken) {
        Toast.show({
          title: 'Push Token Not Provided',
          autoClose: 3000,
          type: ALERT_TYPE.DANGER,
        });
        return;
      }
      const message = {
        to: expoPushToken,
        sound: 'default',
        title,
        body,
        data,
      };
      try {
        await axios.post('https://exp.host/--/api/v2/push/send', message, {
          headers: {
            Accept: 'application/json',
            'Accept-encoding': 'gzip, deflate',
            'Content-Type': 'application/json',
          },
        });
      } catch (error) {
        console.log('Error in sending notification:', error);
      }
    },
    [expoPushToken]
  );

  async function registerPushNotificationsAsync() {
    if (!Device.isDevice) {
      Alert.alert('Must use a physical device');
      return;
    }

    try {
      const token = (await Notifications.getDevicePushTokenAsync()).data;

      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        Alert.alert('Failed to get push token for push notification');
        return;
      }

      if (Platform.OS === 'android') {
        Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250],
          lightColor: '#ff231F7c',
        });
      }

      const projectId =
        Constants?.expoConfig?.extra?.eas?.projectId ??
        Constants?.easConfig?.projectId;
      console.log('projectId', projectId);
      notificationListener.current =
        Notifications.addNotificationReceivedListener(notification => {
          setNotification(notification);
        });
      responseListener.current =
        Notifications.addNotificationResponseReceivedListener(response => {
          console.log('addNotificationResponseReceivedListener', response);
        });

      AsyncStorage.getItem('notificationToken').then(res => {
        // only saving token if there is no token saved in localStorage
        if (!res) {
          AsyncStorage.setItem('notificationToken', token)
            .then(async () => {
              await axiosRequest({
                method: 'put',
                url: '/notifications',
                data: { token: token },
              }).then(() => console.log('token sent', token));
            })
            .catch(err => {
              Toast.show({
                title: 'Notification Saving Failed',
                textBody: err.message,
                type: ALERT_TYPE.DANGER,
                autoClose: 2000,
              });
            });
        }
      });
      setExpoPushToken(token);
      return token;
    } catch (error: any) {
      console.error(error, 'Error in Notification');
      Toast.show({
        title: error.message,
        type: ALERT_TYPE.DANGER,
        autoClose: 3000,
      });
    }
  }
  // Local Scheduled Notification
  const scheduleNotification = React.useCallback(
    async ({
      notificationTitle,
      notificationMessage,
      triggerTime,
      repeat = false,
    }: {
      notificationTitle?: string;
      notificationMessage?: string;
      triggerTime: number;
      repeat?: boolean;
    }) => {
      const schedulingOptions = {
        identifier: 'review',
        content: {
          title: notificationTitle,
          body: notificationMessage,
          data: { data: 'goes here' },
        },
        trigger: {
          seconds: triggerTime,
          repeats: repeat,
        },
      };

      await Notifications.scheduleNotificationAsync(schedulingOptions);
    },
    []
  );

  return {
    expoPushToken,
    notification,
    scheduleNotification,
    sendPushNotification,
    registerPushNotificationsAsync,
  };
};

export default usePushNotifications;
