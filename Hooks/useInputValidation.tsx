import { useState } from 'react';

type ValidationFunction<T> = (value: T) => boolean;
type ErrorMessage = string;

interface ValidationRule<T> {
  validate: ValidationFunction<T>;
  errorMessage: ErrorMessage;
}

export const useInputValidation = <T,>(
  initialValue: T,
  validationRules: ValidationRule<T>[]
) => {
  const [value, setValue] = useState<T>(initialValue);
  const [error, setError] = useState<string | null>(null);

  const validateInput = (inputValue: T) => {
    for (const rule of validationRules) {
      if (!rule.validate(inputValue)) {
        setError(rule.errorMessage);
        return false;
      }
    }
    setError(null);
    return true;
  };

  const handleChange = (inputValue: T) => {
    setValue(inputValue);
    validateInput(inputValue);
  };

  return {
    value,
    error,
    handleChange,
    isValid: error === null,
  };
};

/*

import React from 'react';
import { useInputValidation } from './useInputValidation';

const MyComponent: React.FC = () => {
  const validationRules = [
    {
      validate: (value: string) => value.trim() !== '',
      errorMessage: 'Field cannot be empty',
    },
    {
      validate: (value: string) => value.length >= 5,
      errorMessage: 'Field must be at least 5 characters long',
    },
  ];

  const { value, error, handleChange, isValid } = useInputValidation<string>('', validationRules);

  return (
    <div>
      <input type="text" value={value} onChange={(e) => handleChange(e.target.value)} />
      {error && <p>{error}</p>}
      <button disabled={!isValid}>Submit</button>
    </div>
  );
};

export default MyComponent;

*/
