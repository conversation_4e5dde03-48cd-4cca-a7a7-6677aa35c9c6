import { useCallback, useState, useEffect } from 'react';

import axios from 'axios';

import * as BackgroundFetch from 'expo-background-fetch';
import * as Network from 'expo-network';
import * as TaskManager from 'expo-task-manager';

import usePushNotifications from './usePushNotifications';

const BACKGROUND_FETCH_TASK = 'background-fetch';

TaskManager.defineTask(BACKGROUND_FETCH_TASK, async () => {
  try {
    console.log('Background fetch triggered');

    const networkStatus = await Network.getNetworkStateAsync();
    console.log('Network status:', networkStatus);

    // Fetch data from the server
    const response = await axios.get('http://192.168.1.136:3000/api');
    const data = await response.data;
    console.log('Fetched data:', data);

    // Schedule a notification for testing
    const { scheduleNotification } = usePushNotifications();
    await scheduleNotification({
      notificationMessage: 'Background fetch triggered successfully',
      notificationTitle: 'Background Task',
      triggerTime: 1,
      repeat: false,
    });

    return BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    console.error('Background fetch error:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

const useBackgroundFetch = () => {
  const [isRegistered, setIsRegistered] = useState(false);
  const [status, setStatus] = useState(null);

  const registerBackgroundFetchAsync = useCallback(async () => {
    try {
      await BackgroundFetch.registerTaskAsync(BACKGROUND_FETCH_TASK, {
        minimumInterval: 1 * 60, // 1 minute for testing
        stopOnTerminate: false, // continue background fetch even after app is killed
        startOnBoot: true, // restart task when the device boots up
      });
      console.log('Background fetch task registered');
    } catch (err) {
      console.error('Error registering background fetch:', err);
    }
  }, []);

  const checkStatusAsync = useCallback(async () => {
    const status = await BackgroundFetch.getStatusAsync();
    const isRegistered = await TaskManager.isTaskRegisteredAsync(
      BACKGROUND_FETCH_TASK
    );
    setStatus(status);
    setIsRegistered(isRegistered);
    console.log('Background fetch status:', status);
    console.log('Is task registered:', isRegistered);
  }, []);

  useEffect(() => {
    checkStatusAsync();
  }, [checkStatusAsync]);

  return {
    registerBackgroundFetchAsync,
    checkStatusAsync,
    status,
    isRegistered,
  };
};

export default useBackgroundFetch;
