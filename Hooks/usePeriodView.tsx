import { useCallback, useEffect, useState } from 'react';
import { Period } from 'Redux/Slices/userSlice';
import { useAuthStore } from '@/stores/useAuthStore';

const days = ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

const usePeriodView = () => {
  const { user } = useAuthStore();
  const [period, setPeriod] = useState<Period>({
    _id: '',
    disabled: false,
    endNotice: '',
    endTime: '',
    startNotice: '',
    startTime: '',
    timetable: '',
    periodCount: 0,
    daysOfWeek: [],
  });
  const [loading, setLoading] = useState<boolean>(true); // Add loading state

  const isTimeInRange = useCallback((time: string, notice: string) => {
    const [hours, minutes] = time?.split(':')?.map(Number);
    const startTime = new Date();
    startTime.setHours(hours);
    startTime.setMinutes(minutes);

    if (!notice) {
      return startTime;
    }

    const [noticeHours, noticeMinutes] = notice.split(':').map(Number);
    const noticeAdjustedStartTime = new Date(startTime);
    noticeAdjustedStartTime.setHours(
      noticeHours - startTime.getHours() === 0
        ? startTime.getHours()
        : startTime.getHours() + (noticeHours - startTime.getHours())
    );
    noticeAdjustedStartTime.setMinutes(startTime.getMinutes() + noticeMinutes);

    return noticeAdjustedStartTime;
  }, []);

  useEffect(() => {
    const currentTime = new Date();
    const currentDay = currentTime.getDay();
    if (!user?.timeTables?.length) {
      setLoading(false); // No data to process
      return;
    }
    if (Array.isArray(user.timeTables)) {
      user?.timeTables?.forEach(table => {
        if (!table) return;
        table?.periods?.forEach((period, indx) => {
          const startTime = isTimeInRange(period.startTime, period.startNotice);
          const endTime = isTimeInRange(period.endTime, period.endNotice);
          // To Start background location tracking

          if (
            currentTime >= startTime &&
            currentTime <= endTime &&
            period.daysOfWeek[currentDay]?.toLocaleLowerCase() ===
              days[currentDay]
          ) {
            setPeriod({
              ...period,
              periodCount: indx + 1,
            });
          } else {
            setPeriod({
              ...period,
              periodCount: indx,
            });
          }
        });
      });
    }

    setLoading(false); // Data has been processed
  }, [user?.timeTables, isTimeInRange]);

  return { period, loading }; // Return the loading state as well
};

export default usePeriodView;
