import { I18n } from 'i18n-js';

import * as Localization from 'expo-localization';

import ar from '../locales/ar.json';
import en from '../locales/en.json';

export const translations = {
  en: en,
  ar: ar,
};
const i18n = new I18n(translations);

i18n.locale = Localization.locale;

i18n.enableFallback = true;
// To see the fallback mechanism uncomment the line below to force the app to use the Japanese language.
i18n.locale = 'en';

export default i18n;
