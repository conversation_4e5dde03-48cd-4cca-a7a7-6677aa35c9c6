import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const useLanguage = () => {
  const [isArabic, setIsArabic] = useState<boolean>(false);
  const { i18n } = useTranslation();

  React.useEffect(() => {
    if (i18n.language === 'ar') {
      setIsArabic(true);
    } else {
      setIsArabic(false);
    }
  }, []);

  return { isArabic };
};

export default useLanguage;
