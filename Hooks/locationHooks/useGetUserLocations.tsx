import { useInfiniteQuery } from '@tanstack/react-query';

import { getUserAttendanceLocationsAPI } from '@/service/locationsAPI';

const useGetUserLocations = () => {
  const query = useInfiniteQuery({
    queryKey: ['userLocations'],
    queryFn: async ({ pageParam = 1 }) =>
      getUserAttendanceLocationsAPI({ skip: (pageParam - 1) * 5, limit: 5 }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // If the API returns an array, check if we got a full page
      // Adjust this logic if your API returns a different structure
      const hasMore = Array.isArray(lastPage) ? lastPage.length === 5 : false;
      return hasMore ? allPages.length + 1 : undefined;
    },
  });
  return { ...query };
};

export default useGetUserLocations;
