import { useEffect } from 'react';

import { usePathname } from 'expo-router';

import { socket } from '../utils/useSocket';

import { notificationsActions } from '../Redux/Slices/NotificationsSlice';
import { useAppDispatch } from '../Redux/Store';
import { socketEventsList } from '../SocketEvents';

import usePushNotifications from './usePushNotifications';

const mutedRoutes = ['/TicketsListScreen', '/ChatScreen'];

const useSocketNotifications = () => {
  const dispatch = useAppDispatch();
  const path = usePathname();
  const { scheduleNotification } = usePushNotifications();

  // Handle receiving messages (tickets)
  useEffect(() => {
    if (!socket || !socket.connected) return;

    const handleReceiveMessage = (message: any) => {
      if (mutedRoutes.includes(path)) return;

      scheduleNotification({
        notificationMessage: 'New Ticket Created',
        notificationTitle: 'New Ticket',
        triggerTime: 1,
      });

      dispatch(notificationsActions.incrementOrDecrement(+1));
      dispatch(notificationsActions.setNotificationsList(message));
    };

    socket.on(socketEventsList.tickets.create.on, handleReceiveMessage);

    return () => {
      socket.off(socketEventsList.tickets.create.on, handleReceiveMessage);
    };
  }, [dispatch, path, scheduleNotification]);

  // Handle receiving replies
  useEffect(() => {
    if (!socket || !socket.connected) return;

    const handleReceivedReplay = (replay: any) => {
      if (mutedRoutes.includes(path)) return;

      scheduleNotification({
        notificationMessage: 'New Replay Created',
        notificationTitle: 'New Replay',
        triggerTime: 1,
      });

      dispatch(notificationsActions.incrementOrDecrement(+1));
      dispatch(notificationsActions.setNotificationsList(replay));
    };

    socket.on(socketEventsList.replays.create.on, handleReceivedReplay);

    return () => {
      socket.off(socketEventsList.replays.create.on, handleReceivedReplay);
    };
  }, [dispatch, path, scheduleNotification]);

  // Ensure the socket is connected
};

export default useSocketNotifications;
