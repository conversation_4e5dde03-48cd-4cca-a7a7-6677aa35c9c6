import React from 'react';

import * as Location from 'expo-location';

import { locationActions } from '../Redux/Slices/locationSlice';
import { useAppDispatch } from '../Redux/Store';

export const useLocation = () => {
  const [location, setLocation] =
    React.useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = React.useState<null | string>(null);
  const [loading, setLoading] = React.useState<boolean>(false);
  const dispatch = useAppDispatch();

  const GetUserCurrentLocation = React.useCallback(async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();

    if (status !== 'granted') {
      setErrorMsg('Permission to access location was denied');
      return;
    }
    try {
      setLoading(true);
      let location = await Location.getCurrentPositionAsync({}).finally(() =>
        setLoading(false)
      );

      setLocation(location);
      dispatch(locationActions.setCurrentLocation(location));
    } catch (error: any) {
      console.error(error.message);
    }
  }, []);

  React.useEffect(() => {
    GetUserCurrentLocation();
  }, []);

  return {
    location,
    setLocation,
    errorMsg,
    loading,
    GetUserCurrentLocation,
  };
};
