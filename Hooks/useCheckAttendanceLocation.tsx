import React from 'react';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';

import * as geolib from 'geolib';

import Loading from '../Layouts/Loading/Loading';
import { useAppSelector } from '../Redux/Store';

import { useLocation } from './useLocation';

const useCheckAttendanceLocation = () => {
  const locations = useAppSelector(state => state.user.data?.locations);
  const [loading, setLoading] = React.useState<boolean>(false);
  const { location, GetUserCurrentLocation } = useLocation();

  {
    loading ? <Loading /> : <></>;
  }

  const isUserInLocation = () => {
    if (!locations) {
      Toast.show({
        title: 'No Locations assigned to user',
        autoClose: 2000,
        type: ALERT_TYPE.DANGER,
        textBody:
          'No Locations Assigned To user in data base contact Your Manger',
      });
      return;
    }

    return locations?.map(async loc => {
      await GetUserCurrentLocation().then(res => {});

      let distanceInMeter = geolib.getDistance(
        { latitude: loc.lat, longitude: loc.lng },
        {
          latitude: location?.coords?.latitude ?? 0,
          longitude: location?.coords?.longitude ?? 0,
        }
      );
      // check if distance in meter <= the locations from database  radius

      return {
        validDistance: distanceInMeter <= Number(loc.radius ?? 10),
        distance: distanceInMeter,
      };
    });
  };

  React.useEffect(() => {
    if (!location?.coords?.latitude) {
      GetUserCurrentLocation();
    }
  }, [location?.coords]);

  return { loading, setLoading, isUserInLocation };
};

export default useCheckAttendanceLocation;
