import { useMutation,  } from '@tanstack/react-query';

import axiosInstance from '../../utils/axios/axiosInstance';
import { handleError } from '../../utils/errors/errorHandler';

import { IUserRequests } from '../../types/Index';
import { IPostUserRequestPayload } from '@/app/(screens)/(user-requests)/LeaveRequestScreen';

const usePostUserRequest = () => {
  const mutation = useMutation({
    mutationKey: ['post-user-requests'],
    mutationFn: postUserRequest,
  });
  return { ...mutation };
};

const postUserRequest = async (payload:IPostUserRequestPayload): Promise<IUserRequests[] | undefined> => {
  try {
    const response = await axiosInstance.post('/users/requests',payload);
    return response.data;
  } catch (error) {
    console.error('Error getting user request', error);
    handleError(error);
  }
};

export default usePostUserRequest;
