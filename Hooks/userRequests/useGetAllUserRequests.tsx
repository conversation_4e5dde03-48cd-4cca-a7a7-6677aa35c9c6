import { useQuery } from '@tanstack/react-query';

import axiosInstance from '../../utils/axios/axiosInstance';
import { handleError } from '../../utils/errors/errorHandler';

import { IUserRequests } from '../../types/Index';

const useGetAllUserRequests = () => {
  const query = useQuery({
    queryKey: ['user-requests'],
    queryFn: getUserRequestsAPI,
  });
  return { ...query };
};

type IResponse = {
  data: IUserRequests[];
};

const getUserRequestsAPI = async (): Promise<IResponse | undefined> => {
  try {
    const response = await axiosInstance.get('/users/requests');
    return response.data;
  } catch (error) {
    console.error('Error getting user request', error);
    handleError(error);
  }
};

export default useGetAllUserRequests;
