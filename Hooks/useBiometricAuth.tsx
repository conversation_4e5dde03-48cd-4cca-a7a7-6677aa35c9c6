import React from 'react';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';

import * as LocalAuthentication from 'expo-local-authentication';

const useBiometricAuth = () => {
  const [isCompatible, setIsCompatible] = React.useState<boolean>(false);
  const [authenticated, setIsAuthenticated] = React.useState<boolean>(false);

  React.useEffect(() => {
    const checkBiometricCompatibility = async () => {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      setIsCompatible(compatible);
    };

    checkBiometricCompatibility();
  }, []);

  const onAuthenticate = async () => {
    try {
      const authResult = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Application Requires FaceID or Fingerprint',
        fallbackLabel: 'Enter Your Password',
      });

      setIsAuthenticated(authResult.success);

      return authResult.success;
    } catch (error: any) {
      Toast.show({
        title: error.message,
        type: ALERT_TYPE.DANGER,
        autoClose: 2000,
      });
      console.error('FaceID or Fingerprint Error:', error);
      return false;
    }
  };

  return {
    isCompatible,
    authenticated,
    onAuthenticate,
  };
};

export default useBiometricAuth;
