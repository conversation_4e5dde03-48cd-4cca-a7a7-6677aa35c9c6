import { useEffect } from 'react';
import { Alert } from 'react-native';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';

import { t } from 'i18next';

import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';

export interface LocationTypes {
  locations: { coords: Coords; timestamp: number };
}

export interface Coords {
  accuracy: number;
  altitude: number;
  altitudeAccuracy: number;
  heading: number;
  latitude: number;
  longitude: number;
  speed: number;
}

const LOCATION_TASK_NAME = 'background-location-task';

TaskManager.defineTask(
  LOCATION_TASK_NAME,
  async ({ data, error }: { data: LocationTypes; error: any }) => {
    if (error) {
      console.error(error);
      return;
    }
    if (data) {
      const { locations } = data;
      console.log('Received new locations', locations);
      // You can send location data to your backend here
    }
  }
);

export const useBackgroundLocation = (
  workStartTime: string,
  workEndTime: string
) => {
  useEffect(() => {
    const startBackgroundTask = async () => {
      const { status } = await Location.requestBackgroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission to access location was denied');
        Toast.show({
          title: t('permission-to-access-location-denied'),
          type: ALERT_TYPE.DANGER,
          autoClose: 3000,
        });
        return;
      }

      // Calculate if current time is within the work period
      const isWithinWorkPeriod = () => {
        const now = new Date();
        const [startHour, startMinute] = workStartTime.split(':').map(Number);
        const [endHour, endMinute] = workEndTime.split(':').map(Number);

        const startTime = new Date();
        startTime.setHours(startHour, startMinute, 0);

        const endTime = new Date();
        endTime.setHours(endHour, endMinute, 0);

        return now >= startTime && now <= endTime;
      };

      if (isWithinWorkPeriod()) {
        await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
          accuracy: Location.Accuracy.High,
          timeInterval: 20 * 60 * 1000, // 20 minutes
          distanceInterval: 0, // Fetch on time interval rather than distance
        });
      } else {
        await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
      }
    };

    startBackgroundTask();

    return () => {
      Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
    };
  }, [workStartTime, workEndTime]);
};
