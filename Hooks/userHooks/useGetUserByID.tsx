import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';

import { getUserByIdAPI } from '@/service/usersAPI';
import { useAuthStore } from '@/stores/useAuthStore';

const useGetUserByID = (userId: string) => {
  const { setUser } = useAuthStore();

  const query = useQuery({
    queryKey: ['user', userId],

    queryFn: async () => getUserByIdAPI(userId),
    enabled: !!userId, // Only run the query if userId is provided
  });

  useEffect(() => {
    if (query.isSuccess && query.data) {
      setUser(query.data);
    }
  }, [query.data, query.isSuccess]);

  return { ...query };
};

export default useGetUserByID;
