import { useMutation, useQueryClient } from '@tanstack/react-query';

import axiosInstance from '../../utils/axios/axiosInstance';

type Payload = {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  avatar: string;
};

const useUpdateUser = (userId: string) => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationKey: ['user', userId],
    mutationFn: async (form: Payload) =>
      await axiosInstance.put(`/user/${userId}`, form),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', userId] });
    },
  });

  return { ...mutation };
};

export default useUpdateUser;
