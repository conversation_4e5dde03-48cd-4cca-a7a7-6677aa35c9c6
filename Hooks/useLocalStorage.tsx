import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';

const useLocalStorage = () => {
  const SET_LOCALSTORAGE_DATA = React.useCallback(
    async (key: string, value: any) => {
      try {
        const jsonValue = JSON.stringify(value);
        await AsyncStorage.setItem(key, jsonValue);
      } catch (e) {}
    },
    []
  );
  const GET_LOCALSTORAGE_DATA = React.useCallback(async (key: string) => {
    try {
      const value = await AsyncStorage.getItem(key);
      if (value !== null) {
        // console.log(value);
      }
    } catch (e) {
      console.log('ERROR GET LOCAL_STORAGE:', e);
      // error reading value
    }
  }, []);

  const DELETE_LOCALSTORAGE_ITEM = React.useCallback(async (key: string) => {
    try {
      const value = await AsyncStorage.removeItem(key);
      if (value !== null) {
        // value previously stored
      }
    } catch (e) {
      // error reading value
    }
  }, []);

  return {
    GET_LOCALSTORAGE_DATA,
    SET_LOCALSTORAGE_DATA,
    DELETE_LOCALSTORAGE_ITEM,
  };
};

export default useLocalStorage;
