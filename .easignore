# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/
.env
android
ios
# Expo
.expo/
dist/
web-build/
/android

/android/app/google-services.json
/attendance-199b2-firebase-adminsdk-6rv8u-575af98834.json
/ios
# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo


attendance-199b2-firebase-adminsdk-6rv8u-5d3a770774.json
credentials.json