import type { TypedUseSelectorHook } from 'react-redux';
import { useDispatch, useSelector } from 'react-redux';

import { configureStore } from '@reduxjs/toolkit';

import AttendanceSlice from './Slices/AttendanceSlice';
import NotificationsSlice from './Slices/NotificationsSlice';
import locationSlice from './Slices/locationSlice';
import userSlice from './Slices/userSlice';

export const store = configureStore({
  reducer: {
    user: userSlice,
    location: locationSlice,
    attendance: AttendanceSlice,
    notifications: NotificationsSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
