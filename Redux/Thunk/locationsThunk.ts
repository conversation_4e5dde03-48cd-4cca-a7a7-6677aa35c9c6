import { createAsyncThunk } from '@reduxjs/toolkit';

import { axiosRequest } from '@/utils/useAxiosFetch';

import { RootState } from '../Store';

export const getAttendanceLocationsThunk = createAsyncThunk(
  'getAttendanceLocationsThunk',
  async (payload: { skip: number; limit: number }, { rejectWithValue }) => {
    try {
      const response = await axiosRequest({
        method: 'GET',
        url: `/attendance?skip=${payload?.skip ?? 0}&limit=${payload?.limit ?? 5}`,
      });
      return response?.data;
    } catch (error: any) {
      // Use rejectWithValue to pass the error to the rejected action
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const getLocationsByUserID = createAsyncThunk(
  'getLocationByID',
  async (_payload: any, thunk) => {
    const state = thunk.getState() as RootState;
    const _id = state.user?.data._id;
    try {
      const response = await axiosRequest({
        method: 'GET',
        url: `/locations/${_id}`,
      });
      return response.data;
    } catch (error) {
      return error;
    }
  }
);
