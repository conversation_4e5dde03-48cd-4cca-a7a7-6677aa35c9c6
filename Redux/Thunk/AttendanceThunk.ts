import { createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from 'Redux/Store';

import { axiosRequest } from '@/utils/useAxiosFetch';

export const getTimeTableByIDThunk = createAsyncThunk(
  'getTimeTableByIDThunk',
  async (_payload, thunkAPI) => {
    const state = thunkAPI.getState() as RootState;
    const { timeTables } = state.user.data;

    try {
      const response = await axiosRequest({
        method: 'GET',
        url: `/timetables/${timeTables[0]}`,
      });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching attendance:', error.message);

      // Handle the 403 error by returning a specific message
      if (error?.response?.status === 403) {
        return thunkAPI.rejectWithValue({
          message: 'You do not have permission to access this resource.',
        });
      }

      return thunkAPI.rejectWithValue({
        message: error.message,
      });
    }
  }
);
