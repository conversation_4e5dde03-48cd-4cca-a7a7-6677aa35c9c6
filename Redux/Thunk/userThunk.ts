import { createAsyncThunk } from '@reduxjs/toolkit';

import axiosInstance from '@/utils/axios/axiosInstance';

import { RootState } from '../Store';

interface ILoginPayload {
  phone: string;
  password: string;
  companyKey: string;
}

export const loginThunk = createAsyncThunk(
  'loginThunk',
  async (payload: ILoginPayload) => {
    console.log('Payload => ', payload);
    try {
      const response = await axiosInstance.post('/login', payload, {
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      return error;
    }
  }
);

export const logoutThunk = createAsyncThunk('logoutThunk', async () => {
  try {
    const response = await axiosInstance.get('/logout');

    return response.data;
  } catch (error: any) {
    console.error('Logout Error', error);
    return error;
  }
});

export const getUserDataByIDThunk = createAsyncThunk(
  'getUserDataByIDThunk',
  async (payload: { _id: string }) => {
    try {
      const response = await axiosInstance.get(`/users/${payload._id}`);
      return response.data;
    } catch (error) {
      return error;
    }
  }
);

interface IEditUserData {
  fullName?: String;
  email?: String;
  phone?: String;
  address?: String;
}

export const editUserDataThunk = createAsyncThunk(
  'editUserDataThunk',
  async (payload: IEditUserData, thunk) => {
    const state = thunk.getState() as RootState;
    const _id = state.user.data._id;

    try {
      const response = await axiosInstance.put(`/users/${_id}`, payload);
      return response.data;
    } catch (error) {
      return error;
    }
  }
);

export const refreshThunk = createAsyncThunk('refreshThunk', async () => {
  try {
    const response = await axiosInstance.get(`/refresh`, {
      withCredentials: true,
      timeout: 3000,
    });

    return response.data;
  } catch (error: any) {
    return null;
  }
});

export const checkInThunk = createAsyncThunk(
  'checkInThunk',
  async (payload: { lat: number; lng: number }) => {
    try {
      const response = await axiosInstance.post(`/attendance/checkIn`, payload);
      return response.data;
    } catch (error) {
      return error;
    }
  }
);
