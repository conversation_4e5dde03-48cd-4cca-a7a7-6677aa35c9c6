import { createSlice } from '@reduxjs/toolkit';

import { getTimeTableByIDThunk } from '../Thunk/AttendanceThunk';

const initialState: any = {
  loading: false,
  attendance: [],
};
const attendanceSlice = createSlice({
  name: 'attendance',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder.addCase(getTimeTableByIDThunk.pending, (state, action) => {
      state.loading = true;
    });
    builder.addCase(getTimeTableByIDThunk.rejected, (state, action) => {
      state.loading = false;
    });
    builder.addCase(getTimeTableByIDThunk.fulfilled, (state, action) => {
      state.loading = false;
      if (action.payload) {
        state.attendance = action.payload;
      }
    });
  },
});

export default attendanceSlice.reducer;
export const attendanceActions = attendanceSlice.actions;
