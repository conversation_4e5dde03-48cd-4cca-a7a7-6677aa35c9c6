import { createSlice } from '@reduxjs/toolkit';

export interface INotificationItem {
  _id: string;
  body: string;
  createdAt: Date;
  modified: boolean;
  priority: string;
  sender: string;
  status: string;
  title: string;
  updatedAt: Date;
}

const initialState = {
  count: 0,
  notificationList: [] as INotificationItem[],
};
const notificationsSlice = createSlice({
  initialState,
  name: 'notifications',
  reducers: {
    incrementOrDecrement: (state, action: { payload: number }) => {
      state.count = state.count + action.payload;
    },
    clearNotification: state => {
      state.count = 0;
    },
    setNotificationsList: (state, action) => {
      if (state.notificationList.length === 0) {
        state.notificationList = [action.payload];
        return;
      }
      state.notificationList = [...state.notificationList, action.payload];
    },
  },
});

export default notificationsSlice.reducer;
export const notificationsActions = notificationsSlice.actions;
