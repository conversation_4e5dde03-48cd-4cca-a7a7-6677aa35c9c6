import { createSlice } from '@reduxjs/toolkit';

import * as Location from 'expo-location';

import { getAttendanceLocationsThunk } from '../Thunk/locationsThunk';

export interface ILocations {
  _id: string;
  user: string;
  location: {
    _id: string;
    lat: number;
    lng: number;
    radius: number;
    title: string;
    description: string;
  };
  attendedBy: string;
  startedAt: Date;
  status: 'attend' | 'absent';
  absent: boolean;
  day: string;
  dayName: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IAttendanceLocations {
  _id: string;
  absent: boolean;
  attendedBy: string;
  createdAt: Date;
  day: string;
  dayName: string;
  location: ILocation;
  startedAt: Date;
  status: string;
  updatedAt: Date;
  user: string;
}

export interface ILocation {
  __v: number;
  _id: string;
  createdAt: Date;
  description: string;
  lat: number;
  lng: number;
  radius: string;
  title: string;
  updatedAt: Date;
}

interface initialStateTypes {
  currentLocation: Location.LocationObject;
  attendanceLocations: { data: IAttendanceLocations[]; length: number };
  loading: boolean;
}
const initialState: initialStateTypes = {
  loading: false,
  attendanceLocations: { data: [], length: 0 },
  currentLocation: {
    timestamp: 0,
    coords: {
      latitude: 0,
      longitude: 0,
      accuracy: 0,
      speed: 0,
      altitude: 0,
      altitudeAccuracy: 0,
      heading: 0,
    },
  },
};
const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    setCurrentLocation: (state, action) => {
      state.currentLocation = action.payload;
    },
    addLocationToState: (state, action: { payload: IAttendanceLocations }) => {
      state.attendanceLocations = {
        data: [...state.attendanceLocations.data, action.payload],
        length: state.attendanceLocations.length + 1,
      };
    },
  },
  extraReducers(builder) {
    builder.addCase(getAttendanceLocationsThunk.pending, state => {
      // Handle the pending state
    });

    builder.addCase(getAttendanceLocationsThunk.rejected, (state, action) => {
      console.error('Error:', action.payload); // Logs the error from rejectWithValue
      state.attendanceLocations.data = [];
      state.attendanceLocations.length = 0;
    });

    builder.addCase(
      getAttendanceLocationsThunk.fulfilled,
      (
        state,
        action: { payload: { data: IAttendanceLocations[]; length: number } }
      ) => {
        if (action?.payload?.data !== undefined) {
          if (state.attendanceLocations.data.length > 0) {
            const existingData = state.attendanceLocations.data;

            // Filter out items that already exist in the state based on their unique _id
            const newData = action.payload.data.filter(
              (newItem: any) =>
                !existingData.some(
                  (existingItem: any) => existingItem._id === newItem._id
                )
            );

            // Append only the new unique data
            state.attendanceLocations.data = [...existingData, ...newData];
          } else {
            state.attendanceLocations.data = action.payload?.data;
          }
          state.attendanceLocations.length = action.payload?.length;
        }
      }
    );
  },
});

export default locationSlice.reducer;
export const locationActions = locationSlice.actions;
