import AsyncStorage from '@react-native-async-storage/async-storage';

import { createSlice } from '@reduxjs/toolkit';
import { getTimeTableByIDThunk } from 'Redux/Thunk/AttendanceThunk';

import { connectIo, socket } from '@/utils/useSocket';
import { updateToken } from 'utils/useAxiosFetch';

import {
  checkInThunk,
  editUserDataThunk,
  getUserDataByIDThunk,
  loginThunk,
  refreshThunk,
} from '../Thunk/userThunk';

import { ILocation } from './locationSlice';

export interface ITimeTableTypes {
  _id: string;
  title: string;
  periods: Period[];
  startDate: null;
  endDate: null;
  daysOfWeek: string[];
  disabled: boolean;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface Period {
  _id: string;
  startTime: string;
  endTime: string;
  startNotice: string;
  endNotice: string;
  timetable?: string;
  disabled: boolean;
  daysOfWeek: any[];
  createdAt?: Date;
  updatedAt?: Date;
  periodCount?: number;
}

export interface IUser {
  _id: string;
  accessCode: number;
  address: string;
  avatar: string;
  branches: any[];
  company: string;
  db: string;
  email: string;
  fullName: string;
  jobDescription: string;
  jobTitle: string;
  locations: ILocation[];
  phone: string;
  role: string;
  section: string;
  timeTables: ITimeTableTypes[];
}

export interface initialStateTypes {
  loading: boolean;
  status: null | number;
  token: string;
  data: IUser;
}

const initialState: initialStateTypes = {
  loading: false,
  status: null,
  token: '',
  data: {} as IUser,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserData: (state, action: { payload: IUser }) => {
      if (action.payload?._id) {
        state.data = action.payload;
      } else {
        return;
      }
    },
    setToken: (state, action: { payload: string | null }) => {
      if (action.payload !== null) {
        state.token = action.payload;
        socket.auth = cb => cb({ token: action.payload });
      }

      if (!socket.connected) {
        connectIo();
      }
    },
    refreshToken: (state, action) => {
      state.token = action.payload;
    },
    signOut: state => {
      state.token = '';
      state.data = initialState.data;
      AsyncStorage.removeItem('token');
      updateToken(null);
    },
    editUserData: (state, action) => {
      const newData = { ...state.data, ...action.payload };
      state.data = newData;
      AsyncStorage.setItem('user', JSON.stringify(newData));
    },
  },
  extraReducers: builder => {
    builder.addCase(loginThunk.pending, status => {
      status.loading = true;
      status.status = null;
      status.data = initialState.data;
    });
    builder.addCase(loginThunk.rejected, status => {
      status.loading = false;
      status.status = 404;
      status.data = initialState.data;
    });
    builder.addCase(loginThunk.fulfilled, (status, action) => {
      status.loading = false;
      status.status = 200;
      if (action?.payload?.user) {
        status.data = action.payload.user;
        status.token = action.payload.token;
        const userData = JSON.stringify(action.payload.user);
        const token = action.payload.token;
        AsyncStorage.setItem('user', userData);
        AsyncStorage.setItem('token', token);
      }
    });

    // Get User Data by his ID
    builder.addCase(getUserDataByIDThunk.pending, (status, action) => {
      status.loading = true;
      status.status = null;
      status.data = initialState.data;
    });
    builder.addCase(getUserDataByIDThunk.rejected, (status, action) => {
      status.loading = false;
      status.status = 404;
      status.data = initialState.data;
    });
    builder.addCase(getUserDataByIDThunk.fulfilled, (status, action) => {
      status.loading = false;
      status.status = 200;
      if (action.payload) {
        status.data = action.payload;
      }
    });

    // Get User Data by his ID
    builder.addCase(refreshThunk.pending, (state, action) => {
      state.loading = true;
      state.data = initialState.data;
    });
    builder.addCase(refreshThunk.rejected, (state, action) => {
      state.loading = false;
      state.data = initialState.data;
    });
    builder.addCase(refreshThunk.fulfilled, (state, action) => {
      state.loading = false;
      state.token = action.payload.token;
    });
    // PUT User Data by his ID
    builder.addCase(editUserDataThunk.pending, (state, action) => {
      state.loading = true;
    });
    builder.addCase(editUserDataThunk.rejected, (state, action) => {
      state.loading = false;
    });
    builder.addCase(editUserDataThunk.fulfilled, (state, action) => {
      state.loading = false;
    });

    // CheckIn Thunk
    builder.addCase(checkInThunk.pending, (status, action) => {
      status.loading = true;
    });
    builder.addCase(checkInThunk.rejected, (status, action) => {
      status.loading = false;
    });
    builder.addCase(checkInThunk.fulfilled, (status, action) => {
      status.loading = false;
    });

    builder.addCase(getTimeTableByIDThunk.pending, (state, action) => {
      state.loading = true;
    });
    builder.addCase(getTimeTableByIDThunk.rejected, (state, action) => {
      state.loading = false;
    });
    builder.addCase(getTimeTableByIDThunk.fulfilled, (state, action) => {
      state.loading = false;
      state.data = { ...state.data, timeTables: [action.payload] };
    });
  },
});

export default userSlice.reducer;
export const userActions = userSlice.actions;
