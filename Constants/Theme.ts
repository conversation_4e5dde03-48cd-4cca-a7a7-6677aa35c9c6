import { Dimensions, PixelRatio } from 'react-native';
import { ms, vs, s } from 'react-native-size-matters';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Responsive normalization for font sizes
const normalize = (size: number) => PixelRatio.getFontScale() * ms(size, 0.5);

function hexToRgba(hex: string, alpha: number): string {
  // Remove "#" if present
  hex = hex.replace(/^#/, '');

  // Expand shorthand form (e.g., "fff") to full form ("ffffff")
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map(char => char + char)
      .join('');
  }

  if (hex.length !== 6) {
    throw new Error('Invalid HEX color');
  }

  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

// Colors
const COLORS = {
  background: '#FFF',
  warning: '#ffcc00',
  primary: '#172554',
  secondary: '#712CFB',
  tertiary: '#573D99',
  blue: '#3B82F6',
  gray: '#D1D1D6',
  gray2: '#C6C6CC',
  white: '#FFFFFF',
  lightWhite: '#F6F6F6',
  grayText: '#8E8E93',
  danger: '#FF3B30',
  success: '#22bb33',
  black: '#000000',
  transparent: 'transparent',
};

// Font sizes (responsive)
const SIZES = {
  xSmall: normalize(10),
  small: normalize(12),
  regular: normalize(14),
  medium: normalize(16),
  large: normalize(18),
  xLarge: normalize(20),
  xxLarge: normalize(32),
  screenWidth: SCREEN_WIDTH,
  screenHeight: SCREEN_HEIGHT,
};

// Font families (you must load these using `expo-font` in App.tsx)
const FONTS = {
  light: 'Cairo-Light',
  regular: 'Cairo-Regular',
  medium: 'Cairo-Medium',
  semiBold: 'Cairo-SemiBold',
  bold: 'Cairo-Bold',
};

// Shadows
const SHADOWS = {
  small: {
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
};

// Buttons
const BUTTONS = {
  primary: {
    backgroundColor: COLORS.primary,
    borderRadius: ms(8),
    borderColor: COLORS.primary,
    borderWidth: 1,
  },
  secondary: {
    backgroundColor: COLORS.transparent,
    borderColor: COLORS.primary,
    borderWidth: 1,
    borderRadius: ms(8),
  },
  danger: {
    backgroundColor: COLORS.danger,
    borderRadius: ms(8),
  },
};

export {
  COLORS,
  SIZES,
  FONTS,
  SHADOWS,
  BUTTONS,
  normalize,
  s,
  ms,
  vs,
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  hexToRgba,
};
