{"expo": {"name": "was-attendance", "slug": "was-attendance", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/adaptive-icon.png", "scheme": "wasattendance", "userInterfaceStyle": "light", "splash": {"image": "./assets/adaptive-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "plugins": ["expo-router", "expo-secure-store", "expo-localization", "expo-font", "expo-sqlite", ["expo-task-manager"], ["expo-background-fetch"], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], ["expo-local-authentication", {"faceIDPermission": "Allow $(PRODUCT_NAME) to use Face ID."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}], ["expo-notifications", {"icon": "./assets/avatar.png", "color": "#082849", "sounds": []}], "react-native-background-geolocation", "react-native-background-fetch"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.was-attendance", "entitlements": {"aps-environment": "development"}, "config": {"usesNonExemptEncryption": false, "googleMapsApiKey": "AIzaSyDykvOMocdC83S6ocyGfbdeqp0VRh2xJkw"}, "infoPlist": {"NSLocationWhenInUseUsageDescription": "We need your location to provide better service.", "NSLocationAlwaysUsageDescription": "We need your location to provide background location updates.", "UIBackgroundModes": ["location", "fetch", "remote-notification", "local-notification"], "CFBundleAllowMixedLocalizations": true, "NSPhotoLibraryUsageDescription": "Allow $(PRODUCT_NAME) to access your photos.", "NSPhotoLibraryAddUsageDescription": "Allow $(PRODUCT_NAME) to save photos.", "NSFaceIDUsageDescription": "Allow $(PRODUCT_NAME) to use Face ID."}}, "android": {"googleServicesFile": "./google-services.json", "package": "com.ahmedmedhat77.wasattendance", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "*.myapp.io", "pathPrefix": "/records"}], "category": ["BROWSABLE", "DEFAULT"]}], "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT", "android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION"]}, "extra": {"eas": {"projectId": "5a967371-d7b4-463d-8c0d-c54892db4817"}}, "owner": "ahmedmedhat77"}}