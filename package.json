{"name": "was-attendance", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.81.5", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "autoprefixer": "^10.4.20", "axios": "^1.6.5", "cliui": "^8.0.1", "expo": "~53.0.18", "expo-background-fetch": "~13.1.6", "expo-camera": "~16.1.10", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-gradle-ext-vars": "^0.1.2", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.7", "expo-local-authentication": "~16.0.5", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-media-library": "~17.1.7", "expo-network": "~7.1.5", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-task-manager": "~13.1.6", "geolib": "^3.3.4", "i18n-js": "^4.3.2", "i18next": "^25.3.1", "install": "^0.13.0", "intl-pluralrules": "^2.0.1", "jwt-decode": "^4.0.0", "nativewind": "^2.0.11", "next-i18next": "^15.3.1", "postcss": "^8.4.41", "react": "19.0.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-native": "0.79.5", "react-native-alert-notification": "^0.4.0", "react-native-background-fetch": "^4.2.5", "react-native-background-geolocation": "^4.17.1", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.20.1", "react-native-paper": "^5.12.5", "react-native-reanimated": "~3.17.5", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.11.1", "react-native-select-dropdown": "^3.4.0", "react-native-size-matters": "^0.4.2", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.1.0", "socket.io-client": "^4.7.5", "stack": "link:@react-navigation/nativereact-navigation/stack", "string-width": "5.1.1", "typescript": "~5.8.3", "validator": "^13.12.0", "yup": "^1.3.3", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/parser": "^7.25.6", "@types/react": "^19.0.14", "@types/validator": "^13.12.2", "babel-plugin-module-resolver": "^5.0.2", "prettier": "^3.3.3", "tailwindcss": "3.3.2"}, "private": true}