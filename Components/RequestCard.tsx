import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

import { COLORS, SIZES } from '@/Constants';
import { hexToRgba } from '@/Constants/Theme';
import i18n from '@/i18n';

import { IUserRequests } from '@/types/Index';

const RequestCard = ({ item }: { item: IUserRequests }) => {
  const getStatusStyle = (status: 'pending' | 'accepted' | 'rejected') => {
    switch (status) {
      case 'pending':
        return styles.pending;
      case 'accepted':
        return styles.accepted;
      case 'rejected':
        return styles.rejected;
      default:
        return {};
    }
  };

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.requestType}>{i18n.t(item.type)}</Text>
        <View style={[styles.statusContainer, getStatusStyle(item.status)]}>
          <Text style={styles.statusText}>{i18n.t(item.status)}</Text>
        </View>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.dateText}>
          {new Date(item.createdAt).toLocaleDateString()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.medium,
    padding: SIZES.medium,
    marginVertical: SIZES.small,
    marginHorizontal: SIZES.medium,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.medium,
  },
  requestType: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statusContainer: {
    paddingHorizontal: SIZES.small,
    paddingVertical: 5,
    borderRadius: SIZES.small,
  },
  statusText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: SIZES.small,
  },
  cardBody: {
    marginTop: SIZES.small,
  },
  dateText: {
    fontSize: SIZES.medium,
    color: hexToRgba(COLORS.black, 0.9),
  },
  pending: {
    backgroundColor: COLORS.warning,
  },
  accepted: {
    backgroundColor: COLORS.success,
  },
  rejected: {
    backgroundColor: COLORS.danger,
  },
});

export default RequestCard;
