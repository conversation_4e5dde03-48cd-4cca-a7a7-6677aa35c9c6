import { useNavigation } from '@react-navigation/native';
import { useState } from 'react';
import { useEffect } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';

import { COLORS, SIZES, globalStyles } from '@/Constants/Theme.ts';
import { useAppSelector } from '@/Redux/Store.ts';

import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as LocalAuthentication from 'expo-local-authentication';

const LockScreen = () => {
  const [code, setCode] = useState<string[]>([]);
  const [error, setError] = useState<string>('');
  const codeLength = Array(6).fill(0);
  const navigation = useNavigation<any>();
  const user = useAppSelector(state => state.user.data);
  const onNumberPress = (number: string) => {
    if (code.length === 6) {
      return;
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCode(old => [...old, number]);
  };

  const onNumberBackSpacePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setCode(code.slice(0, -1));
  };

  const onBiometricPress = async () => {
    const result = await LocalAuthentication.authenticateAsync();

    if (result.success) {
      navigation.navigate('MainScreen');
    } else {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const OFFSET = useSharedValue(0);
  const TIMING = 80;

  const style = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: OFFSET.value }],
    };
  });

  useEffect(() => {
    if (code.length === 6) {
      if (code.join('') === String(user?.accessCode)) {
        setError('');
        navigation.navigate('MainScreen');
        setCode([]);
      }
      // if code is wrong
      else {
        setCode([]);
        setError('Wrong Password !!');
        OFFSET.value = withSequence(
          (OFFSET.value = withSequence(
            withTiming(-OFFSET.value, { duration: TIMING / 2 }), // Extract value from SharedValue
            withRepeat(
              withTiming(OFFSET.value + 50, { duration: TIMING }),
              4,
              true
            ), // Extract value
            withTiming(0, { duration: TIMING }) // No need to extract value here
          ))
        );
      }
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [code]);

  useEffectuseState(() => {
    onBiometricPress();
  }, []);
  return (
    <SafeAreaView style={globalStyles.SafeAreaView}>
      <TouchableOpacity
        onPress={async () => {
          if (navigation.canGoBack()) {
            navigation.goBack();
          } else {
            navigation.navigate('');
            try {
              // await AsyncStorage.removeItem("token");
              // await AsyncStorage.removeItem("user");
            } catch (error) {
              console.log('Error removing from localstorage', error);
            }
          }
        }}
      >
        <MaterialCommunityIcons name="chevron-left" size={30} />
      </TouchableOpacity>
      <View className="py-2 px-5">
        <Text className="text-black text-center text-xl  font-bold my-10">
          Welcome Back {user?.fullName}
        </Text>

        {/* code view */}
        <Animated.View
          className="flex-row justify-center w-full  gap-4"
          style={style}
        >
          {codeLength.map((_, indx) => (
            <View
              key={indx}
              className={`w-5 h-5 rounded-full ${
                code[indx] ? 'bg-blue-500' : 'bg-gray-300 '
              }`}
            ></View>
          ))}
        </Animated.View>

        <Text className="text-red-500 text-center mt-2 font-bold">{error}</Text>

        {/* numbers view */}
        <View className="px-5  mt-10">
          <View className="flex-row   justify-between">
            {[1, 2, 3].map(number => (
              <TouchableOpacity
                style={styles.numberButton}
                key={number}
                onPress={() => onNumberPress(String(number))}
              >
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row   justify-between">
            {[4, 5, 6].map(number => (
              <TouchableOpacity
                style={styles.numberButton}
                key={number}
                onPress={() => onNumberPress(String(number))}
              >
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row   justify-between">
            {[7, 8, 9].map(number => (
              <TouchableOpacity
                style={styles.numberButton}
                key={number}
                onPress={() => onNumberPress(String(number))}
              >
                <Text style={styles.number}>{number}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View className="flex-row   justify-between items-center ">
            <TouchableOpacity
              style={styles.numberButton}
              onPress={onBiometricPress}
            >
              <MaterialCommunityIcons name="face-recognition" size={26} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.numberButton}
              onPress={() => onNumberPress(String(0))}
            >
              <Text style={styles.number}> 0</Text>
            </TouchableOpacity>

            {code.length > 0 ? (
              <TouchableOpacity
                style={styles.numberButton}
                onPress={onNumberBackSpacePress}
              >
                <MaterialCommunityIcons name="backspace-outline" size={26} />
              </TouchableOpacity>
            ) : (
              <View className="min-w-[89px]" />
            )}
          </View>
        </View>
        {/* End oF Number View */}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  numberButton: {
    backgroundColor: '#F2F2F2',
    padding: 33,
    gap: 10,
    borderRadius: 10,
    marginBottom: 10,
  },
  number: {
    fontSize: SIZES.xLarge,
    fontWeight: 'bold',
    color: COLORS.black,
    textAlign: 'center',
  },
});

export default LockScreen;
