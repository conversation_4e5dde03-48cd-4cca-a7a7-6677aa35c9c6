import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

import { Entypo } from '@expo/vector-icons';

import useLanguage from '@/Hooks/useLanguage';

import { COLORS, SIZES } from '@/Constants';

type CardTypes = {
  day: string;
  startedAt: string;
  endedAt: string;
  endTime: string;
  onPress: () => void;
  dayName: string;
  absent: boolean;
  latency?: string;
};

const AttendanceCard = (props: CardTypes) => {
  const { t } = useTranslation();
  const { isArabic } = useLanguage();

  const cardStyle = [
    styles.container,
    props.absent ? styles.absentCard : styles.attendedCard,
  ];

  return (
    <TouchableOpacity style={cardStyle} onPress={props.onPress}>
      <View style={[styles.header, isArabic && styles.rtl]}>
        <Text style={styles.dayText}>{props.day}</Text>
        <Text style={styles.dayNameText}>
          {t(props?.dayName?.toLowerCase())}
        </Text>
      </View>

      <View style={styles.body}>
        <View style={styles.timeContainer}>
          <Text style={styles.timeLabel}>{t('first-attend')}</Text>
          <Text style={styles.timeText}>{props.startedAt}</Text>
        </View>
        <Entypo name="location-pin" size={40} color={COLORS.danger} />
        <View style={styles.timeContainer}>
          <Text style={styles.timeLabel}>{t('last-attend')}</Text>
          <Text style={styles.timeText}>{props.endTime}</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.timeText}>
          {t('latency-time')}: {props.latency}
        </Text>

        <Text style={props.absent ? styles.absentLabel : styles.attendedLabel}>
          {props.absent ? t('absent') : t('attended')}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: SIZES.medium,
    borderRadius: SIZES.xLarge,
    marginBottom: SIZES.small,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  latencyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 150,
  },
  attendedCard: {
    backgroundColor: '#F0F8FF',
    borderColor: COLORS.primary,
  },
  absentCard: {
    backgroundColor: '#FFF0F0',
    borderColor: COLORS.danger,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.small,
  },
  rtl: {
    flexDirection: 'row-reverse',
  },
  dayText: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    color: COLORS.tertiary,
  },
  dayNameText: {
    fontSize: SIZES.medium,
    color: COLORS.black,
    fontWeight: '600',
  },
  body: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeContainer: {
    alignItems: 'center',
  },
  timeLabel: {
    color: COLORS.black,
    marginBottom: SIZES.small,
  },
  timeText: {
    color: COLORS.tertiary,
    fontWeight: 'bold',
  },
  footer: {
    marginTop: SIZES.small,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  attendedLabel: {
    color: COLORS.success,
    fontWeight: 'bold',
  },
  absentLabel: {
    color: COLORS.danger,
    fontWeight: 'bold',
  },
});

export default memo(AttendanceCard);
