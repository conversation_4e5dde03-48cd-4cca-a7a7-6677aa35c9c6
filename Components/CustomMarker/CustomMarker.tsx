import React from 'react';
import { View, Text, Image } from 'react-native';
import { Callout, Circle, Marker } from 'react-native-maps';

import { COLORS } from '@/Constants';
import { useAppSelector } from '@/Redux/Store';
import { IMAGE_URL } from '@/config';

const CustomMarker = props => {
  const user = useAppSelector(state => state.user.data);
  return (
    <>
      <Circle
        center={{ latitude: props?.lat, longitude: props?.lng }}
        radius={props?.radius ?? 50}
        fillColor="green"
        strokeColor="#ddd"
        strokeWidth={10}
      />
      <Marker
        coordinate={{
          latitude: props?.lat,
          longitude: props?.lng,
        }}
      >
        <Callout>
          <View className="justify-center items-center p-2 max-w-[200px]">
            <Text className="font-bold mb-1 text-xl ">
              {props?.title ?? ''}
            </Text>
            <Text className="text-lg font-medium"> WAFI-TOP Company </Text>

            <Image
              source={
                { uri: IMAGE_URL + user?.avatar } ??
                require('@/assets/placeholder-image.jpg')
              }
              className=" object-contain w-20 h-20  rounded-full my-1"
              style={{ borderWidth: 2, borderColor: COLORS.primary }}
            />

            <Text className="text-lg font-semibold text-center">
              {user?.fullName}
            </Text>
          </View>
        </Callout>
      </Marker>
    </>
  );
};

export default CustomMarker;
