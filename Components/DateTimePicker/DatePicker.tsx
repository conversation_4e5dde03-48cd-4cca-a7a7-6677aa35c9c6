import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import React, { useCallback, useEffect, useState } from 'react';
import {
  Modal,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
  Platform,
} from 'react-native';

import { Ionicons } from '@expo/vector-icons';

import { COLORS, s } from '../../Constants/Theme';

interface CustomDatePickerProps {
  initialDate?: Date;
  title?: string;
  onDateChange?: (selectedDate: Date) => void;
  placeholder?: string;
  error?: string;
  is24Hour?: boolean;
  mode?: 'date' | 'time';
  display?: 'default' | 'spinner' | 'calendar' | 'clock';
  value?: Date | null;
  minimumDate?: Date;
  maximumDate?: Date;
}

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  initialDate = new Date(),
  placeholder,
  onDateChange,
  error,
  title,
  mode = 'date',
  is24Hour = false,
  display = 'default',
  value,
  minimumDate,
  maximumDate,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [date, setDate] = useState<Date>(initialDate);

  useEffect(() => {
    if (value) setDate(value);
    else if (mode === 'time') setDate(new Date());
  }, [value, mode]);

  const formatDateTime = useCallback((): string => {
    if (!date) return placeholder || '';
    const options =
      mode === 'time'
        ? {
            hour: '2-digit',
            minute: '2-digit',
            hour12: !is24Hour,
          }
        : {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          };

    return new Intl.DateTimeFormat('default', options as any).format(date);
  }, [date, mode, is24Hour, placeholder]);

  const handleDateChange = (
    event: DateTimePickerEvent,
    selectedDate?: Date
  ) => {
    if (event.type === 'set' && selectedDate) {
      setDate(selectedDate);
      onDateChange?.(selectedDate);
    }
    if (Platform.OS === 'android') {
      setShowModal(false);
    }
  };

  const toggleModal = () => setShowModal(prev => !prev);

  const handleIOSConfirm = () => {
    onDateChange?.(date);
    toggleModal();
  };

  return (
    <View style={styles.wrapper}>
      {title && <Text style={styles.label}>{title}</Text>}

      <TouchableOpacity
        onPress={toggleModal}
        style={[
          styles.inputContainer,
          error && styles.errorBorder,
          showModal && styles.focusedBorder,
        ]}
      >
        <Text style={styles.inputText}>{formatDateTime()}</Text>
        <Ionicons
          name={mode === 'date' ? 'calendar-outline' : 'time-outline'}
          size={s(18)}
          color={COLORS.gray}
        />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      {/* Android Inline Picker */}
      {Platform.OS === 'android' && showModal && (
        <DateTimePicker
          value={date}
          mode={mode}
          display={display}
          is24Hour={is24Hour}
          onChange={handleDateChange}
          minimumDate={minimumDate}
          maximumDate={maximumDate}
        />
      )}

      {/* iOS Modal Picker */}
      {Platform.OS === 'ios' && showModal && (
        <Modal
          transparent
          animationType="slide"
          visible={showModal}
          onRequestClose={toggleModal}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={{ alignItems: 'center' }}>
                <DateTimePicker
                  value={date}
                  mode={mode}
                  display={display}
                  is24Hour={is24Hour}
                  onChange={handleDateChange}
                  minimumDate={minimumDate}
                  maximumDate={maximumDate}
                />
              </View>
              <View style={styles.buttonRow}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={toggleModal}
                >
                  <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.confirmButton]}
                  onPress={handleIOSConfirm}
                >
                  <Text style={[styles.buttonText, { color: 'white' }]}>
                    Confirm
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

export default CustomDatePicker;

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: s(12),
  },
  label: {
    fontSize: s(14),
    fontWeight: '500',
    marginBottom: s(4),
    color: COLORS.primary,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: s(12),
    paddingHorizontal: s(14),
    borderRadius: s(10),
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.gray2,
  },
  inputText: {
    fontSize: s(14),
    color: COLORS.grayText,
  },
  errorText: {
    fontSize: s(12),
    color: COLORS.danger,
    marginTop: s(4),
  },
  focusedBorder: {
    borderColor: COLORS.primary,
  },
  errorBorder: {
    borderColor: COLORS.danger,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: s(16),
    borderTopRightRadius: s(16),
    padding: s(16),
    height: '50%',
    width: '100%',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: s(16),
  },
  button: {
    paddingVertical: s(10),
    paddingHorizontal: s(16),
    borderRadius: s(8),
    marginLeft: s(10),
  },
  cancelButton: {
    backgroundColor: COLORS.gray2,
  },
  confirmButton: {
    backgroundColor: COLORS.primary,
  },
  buttonText: {
    fontSize: s(14),
    color: COLORS.grayText,
  },
});
