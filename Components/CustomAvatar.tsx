import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';
import { Avatar } from 'react-native-paper';

import { COLORS } from 'Constants';

import { IMAGE_URL } from '../config';

interface IAvatarProps {
  avatar?: string;
  fullName: string;
  size?: number;
  outSource?: boolean;
  containerStyles?: object;
}

const CustomAvatar = (props: IAvatarProps) => {
  
  return (
    <View style={[styles.avatarContainer, props?.containerStyles ?? {}]}>
      {props.avatar && props?.avatar?.length > 0 ? (
        <Avatar.Image
          size={props.size}
          source={{
            uri: `${props.outSource ? props?.avatar : `${IMAGE_URL}${props?.avatar}`}`,
          }}
        />
      ) : (
        <Avatar.Text
          size={props.size}
          label={props.fullName?.[0]?.toUpperCase() ?? '?'}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    borderWidth: 2,
    borderColor: COLORS.white,
    borderRadius: 100,
  },
});

export default memo(CustomAvatar);
