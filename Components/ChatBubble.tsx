import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Avatar } from 'react-native-paper';

import { COLORS, SIZES } from '../Constants';
import CustomAvatar from './CustomAvatar';

type IChatBubbleTypes = {
  isCurrentUser: boolean;
  avatar?: string;
  senderName: string;
  replay: string;
  date: string;
};

const ChatBubble = (props: IChatBubbleTypes) => {
  return (
    <View
      style={[
        styles.messageContainer,
        props.isCurrentUser
          ? styles.currentUserMessage
          : styles.otherUserMessage,
      ]}
    >
      <View style={styles.avatarAndNameContainer}>
        <CustomAvatar fullName={props.senderName} avatar={props.avatar} outSource={true}  size={50}/>
        <Text style={styles.senderName}>{props.senderName}</Text>
      </View>

      <Text style={styles.messageText}>{props.replay}</Text>
      <Text style={styles.messageTimestamp}>{props.date}</Text>
    </View>
  );
};

export default ChatBubble;

const styles = StyleSheet.create({
  messageContainer: {
    backgroundColor: COLORS.secondary,
    padding: SIZES.xSmall,
    borderRadius: SIZES.small,
    marginBottom: SIZES.small,
    width: '80%',
  },
  messageText: {
    color: COLORS.white,
    fontSize: SIZES.small,
  },
  messageTimestamp: {
    color: COLORS.gray,
    fontSize: SIZES.xSmall,
    textAlign: 'right',
    marginTop: SIZES.xSmall,
  },
  currentUserMessage: {
    backgroundColor: COLORS.secondary,
    alignSelf: 'flex-end',
  },
  otherUserMessage: {
    backgroundColor: COLORS.primary,
    alignSelf: 'flex-start',
  },
  avatarAndNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.xSmall,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 50,
    marginRight: SIZES.xSmall,
  },
  senderName: {
    fontSize: SIZES.small,
    marginHorizontal: SIZES.xSmall,
    fontWeight: 'bold',
    color: COLORS.white,
  },
});
