import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { TextInput, TextInputProps } from 'react-native-paper';

import { COLORS, SIZES } from '@/Constants';
import i18n from '@/i18n';

interface IProps extends TextInputProps {
  title?: string;
  placeholder?: string;
  value: string;
  handleChangeText: (text: string) => void;
  errorText?: string;

  secureTextEntry?: boolean;
  containerStyles?: ViewStyle;
}

const CustomInput = (props: IProps) => {
  const isArabic = i18n.language === 'ar';

  return (
    <View style={[styles.container, props.containerStyles]}>
      <TextInput
        {...props}
        label={props.title}
        placeholder={props.placeholder}
        value={props.value}
        onChangeText={props.handleChangeText}
        secureTextEntry={props.secureTextEntry}
        mode="outlined"
        outlineColor={props.errorText ? COLORS.danger : COLORS.gray2}
        activeOutlineColor={props.errorText ? COLORS.danger : COLORS.primary}
        error={!!props.errorText}
        style={[styles.input, props.style, { textAlign: isArabic ? 'right' : 'left' }]}
        theme={{
          colors: {
            primary: COLORS.primary,
            background: COLORS.white,
          },
          roundness: SIZES.small,
        }}
      />
      {props.errorText ? (
        <Text style={styles.errorText}>{props.errorText}</Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SIZES.small,
  },
  input: {
    backgroundColor: COLORS.white,
  },
  errorText: {
    color: COLORS.danger,
    fontSize: SIZES.small,
    marginTop: 4,
    paddingHorizontal: SIZES.small,
  },
});

export default CustomInput;
