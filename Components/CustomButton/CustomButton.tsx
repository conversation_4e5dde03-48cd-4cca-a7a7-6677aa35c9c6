import React from 'react';
import { Text } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

interface customButtonTypes {
  title?: string;
  onPress?: () => void;
  children?: JSX.Element;
  bgColor?: string;
  textColor?: string;
  rounded?: string;
  disabled?: any;
}

const CustomButton = ({
  title,
  onPress,
  children,
  bgColor,
  textColor,
  rounded,
  disabled,
}: customButtonTypes) => {
  return (
    <TouchableOpacity
      disabled={disabled}
      className={`w-full rounded ${rounded} py-2`}
      style={{ backgroundColor: bgColor, opacity: disabled ? 0.3 : 1 }}
      onPress={onPress}
    >
      {children}
      <Text
        className="text-lg text-center font-semibold"
        style={{ color: textColor }}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default CustomButton;
