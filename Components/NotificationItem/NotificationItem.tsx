import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import CustomAvatar from 'Components/CustomAvatar';

import { INotificationItem } from '@/app/(screens)/NotificationScreen';

const NotificationItem = (
  props: INotificationItem & { onPress: () => void }
) => {
  const { t } = useTranslation();
  return (
    <TouchableOpacity
      onPress={props.onPress}
      className={` flex items-center  justify-between rounded-lg mb-3 p-2 py-4 ${
        props?.active ? 'bg-white' : 'bg-white'
      }`}
    >
      <View className="flex-row items-center gap-x-2 px-2">
        <CustomAvatar
          avatar={props.from?.avatar}
          fullName={props.from?.fullName ?? props.from?.userName}
        />

        <View className=" py-1 w-[80%] ">
          <Text className="text-black font-semibold mb-1 text-xl">
            {props.from?.fullName}
          </Text>
          <Text className="text-gray-500 font-medium text-sm ">
            {t(props?.path ?? '')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default memo(NotificationItem);
