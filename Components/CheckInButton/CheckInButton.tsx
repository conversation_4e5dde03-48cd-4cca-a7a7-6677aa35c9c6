import React from 'react';
import { useTranslation } from 'react-i18next';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';
import { Button } from 'react-native-paper';

import * as geolib from 'geolib';

import useBiometricAuth from '@/Hooks/useBiometricAuth';
import { useLocation } from '@/Hooks/useLocation';

import { COLORS } from '@/Constants';
import { locationActions } from '@/Redux/Slices/locationSlice';
import { useAppDispatch, useAppSelector } from '@/Redux/Store';
import { checkInThunk } from '@/Redux/Thunk/userThunk';

const _date = new Date();

interface ILocationResponse {
  _id: string;
  absent: boolean;
  attendedBy: string;
  createdAt: Date;
  day: string;
  dayName: string;
  location: string;
  startedAt: Date;
  status: 'attend' | 'absent';
  updatedAt: Date;
  user: string;
}

const CheckInButton = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const userData = useAppSelector(state => state.user.data);
  const locations = useAppSelector(state => state.user.data?.locations);
 

  const { isCompatible, onAuthenticate } = useBiometricAuth();
  const { GetUserCurrentLocation, location } = useLocation();
  const [loading, setLoading] = React.useState<boolean>(false);

  const isUserInLocation = () => {
    if (!locations) {
      Toast.show({
        title: 'No Locations assigned to user',
        autoClose: 2000,
        type: ALERT_TYPE.DANGER,
        textBody:
          'No Locations Assigned To user in data base contact Your Manger',
      });
      return;
    }

    return locations?.map(loc => {
      if (!loc.lat || !loc.lng) return;
      let distanceInMeter = geolib.getDistance(
        { latitude: loc.lat, longitude: loc.lng },
        {
          latitude: location?.coords?.latitude,
          longitude: location?.coords?.longitude,
          lat: location?.coords?.latitude ?? 0,
          lng: location?.coords?.longitude ?? 0,
        }
      );
      // check if distance in meter <= the locations from database  radius

      return {
        validDistance: Number(distanceInMeter ?? 0) <= Number(loc.radius ?? 0),
        distance: Math.ceil(distanceInMeter),
      };
    });
  };

  const checkInHandler = async () => {
    setLoading(true);
    await GetUserCurrentLocation().finally(() => setLoading(false));
    try {
      const isAuthenticated = await onAuthenticate();
      if (isAuthenticated) {
        const isInLocation = isUserInLocation()?.some(
          v => v?.validDistance === true
        );
        if (isInLocation) {
          setLoading(true);
          dispatch(
            checkInThunk({
              lat: location?.coords?.latitude ?? 0,
              lng: location?.coords?.longitude ?? 0,
            })
          )
            .then(res => {
              if (res?.payload as ILocationResponse) {
                dispatch(
                  locationActions.addLocationToState({
                    ...res.payload,
                    location: {
                      lat: location?.coords.latitude ?? 0,
                      lng: location?.coords.longitude ?? 0,
                      title: userData.fullName,
                    },
                  })
                );

                Toast.show({
                  title: `${t('welcome')} ${userData?.fullName ?? ''}`,
                  textBody: `${t('welcome-to')} ${
                    userData.fullName ?? 'User'
                  } \n You Have been registered in ${_date?.toDateString()} `,
                  type: ALERT_TYPE.SUCCESS,
                  autoClose: 3000,
                });
              }
            })
            .catch(err => {
              console.error(err);
              Toast.show({
                title: 'Network Error',
                textBody: err.message,
                type: ALERT_TYPE.DANGER,
                autoClose: 3000,
              });
            })
            .finally(() => setLoading(false));
        } else {
          Toast.show({
            title: t('sorry-you-are-not-in-valid-location'),
            textBody: `${t('sorry')} ${userData.fullName ?? ''}`,
            type: ALERT_TYPE.DANGER,
            autoClose: 3500,
          });
        }
      } else {
        Toast.show({
          title: t('you-are-not-authenticated'),
          textBody: `${t('sorry')} ${t('please-re-login')}`,
          type: ALERT_TYPE.DANGER,
          autoClose: 3500,
        });
      }
    } catch (error: any) {
      Toast.show({
        title: t('re-try-auth-print'),
        textBody: error.message,
        type: ALERT_TYPE.DANGER,
        autoClose: 3000,
      });
    }
  };

  return (
    <Button
      onPress={checkInHandler}
      disabled={!isCompatible || loading}
      loading={loading}
      mode="contained"
      className="rounded-lg"
      buttonColor={loading ? COLORS.primary : COLORS.secondary}
      textColor={COLORS.white}
    >
      {t('check-in')}
    </Button>
  );
};

export default CheckInButton;
