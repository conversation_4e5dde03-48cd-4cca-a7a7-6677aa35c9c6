import React from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text } from 'react-native';

import { Period } from 'Redux/Slices/userSlice';

import useLanguage from '@/Hooks/useLanguage';
import { useBackgroundLocation } from 'Hooks/useBackgroundLocation';
import usePeriodView from 'Hooks/usePeriodView';

import CheckInButton from '../CheckInButton/CheckInButton';

const _date = new Date();

const CheckInComponent = () => {
  const { t } = useTranslation();
  const { isArabic } = useLanguage();
  const { period } = usePeriodView();

  
  useBackgroundLocation(period?.startTime, period?.endTime);

  return (
    <View className="w-full px-2">
      <View className="bg-white rounded  m-auto px-4 py-1 w-full">
        <View
          className={` justify-between items-center  ${
            isArabic ? 'flex-row-reverse' : 'flex-row'
          }`}
        >
          <View className={`${isArabic ? 'items-end ' : 'items-start'}`}>
            <Text className="text-xl font-semibold">
              {t('working-schedule')}
            </Text>
            <Text>
              {t('period')} {period?.periodCount}
            </Text>
          </View>
          <Text className="text-l text-gray-600 font-semibold">
            {_date?.toDateString()}
          </Text>
        </View>

        <View className="mt-4">
          <Text className="text-2xl font-bold text-center">
            {period?.startTime} - {period?.endTime}
          </Text>
        </View>

        <View className="my-3">
          <CheckInButton />
        </View>
      </View>
    </View>
  );
};

export default CheckInComponent;
