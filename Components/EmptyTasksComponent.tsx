import React from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, Image, StyleSheet } from 'react-native';

import emptyTask from '../assets/empty-task.png';

const EmptyTasksComponent = () => {
  const { t } = useTranslation();
  return (
    <View style={styles.emptyListContainer}>
      <Image source={emptyTask} />
      <Text className="text-2xl font-bold"> {t('no-tasks')} !</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EmptyTasksComponent;
