import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';
import { RefreshControl } from 'react-native-gesture-handler';

import { FlashList } from '@shopify/flash-list';

import useLanguage from '../Hooks/useLanguage';

import { SIZES } from '../Constants';
import Loading from '../Layouts/Loading/Loading';

import RequestItem from './RequestItem';
import { IUserRequests } from '@/types/Index';

type MyRequestsTypes = {
  requests: IUserRequests[];
  loading: boolean;
  fetchData: () => void;
};

const MyRequestsScreen = (props: MyRequestsTypes) => {
  const { isArabic } = useLanguage();

  return (
    <>
      {props.loading && <Loading />}
      <View style={[styles.container, { direction: isArabic ? 'rtl' : 'ltr' }]}>
        <FlashList
          contentContainerStyle={{
            paddingHorizontal: SIZES.xSmall,
          }}
          refreshControl={
            <RefreshControl
              refreshing={props.loading}
              onRefresh={props.fetchData}
            />
          }
          showsVerticalScrollIndicator={true}
          estimatedItemSize={200}
          data={props?.requests}
          keyExtractor={item => item?._id}
          renderItem={({ item }) => <RequestItem item={item} />}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,

    marginBottom: SIZES.xSmall,
  },
});

export default memo(MyRequestsScreen);
