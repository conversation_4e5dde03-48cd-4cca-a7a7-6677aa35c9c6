import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

import { t } from 'i18next';

import Entypo from '@expo/vector-icons/Entypo';

import { COLORS, SIZES } from '@/Constants';
// import RenderHTML from "react-native-render-html";
import { IMessageTypes } from '@/app/(tabs)/tickets';

interface chatItemProps extends IMessageTypes {
  onPress: () => any;
}

const TicketItem = (props: chatItemProps) => {
  const statusColor = useCallback(() => {
    switch (props.status) {
      case 'pending':
        return '#F29339';
      case 'rejected':
        return COLORS.danger;
      default:
        return '#28a745';
    }
  }, [props.status]);

  const priorityColor = useCallback(() => {
    switch (props.priority) {
      case 'high':
        return COLORS.danger;
      case 'medium':
        return COLORS.secondary;
      case 'normal':
        return COLORS.blue;
    }
  }, [props.priority]);

  return (
    <TouchableOpacity onPress={props.onPress}>
      <View style={styles.containerStyle}>
        <View
          style={[
            styles.priorityContainer,
            {
              backgroundColor: priorityColor(),
            },
          ]}
        />
        <View style={styles.contentContainer}>
          <View style={styles.infoContainer}>
            <Text style={styles.ticketTitle}>
              {props.title?.length > 20
                ? props.title.slice(0, 20) + '...'
                : props.title}
            </Text>
            <View
              style={{ flexDirection: 'row', gap: 3, alignItems: 'center' }}
            >
              <Entypo name="user" size={24} color={COLORS.gray2} />
              <Text>
                {(props.sender?.userName || props.sender?.fullName) ?? ''}
              </Text>
            </View>
          </View>

          <View
            style={{ justifyContent: 'space-between', height: '100%', gap: 20 }}
          >
            <View style={{ gap: 5 }}>
              <Text style={{ color: COLORS.grayText, fontWeight: 'bold' }}>
                {t('pariority')}:
              </Text>
              <Text style={{ color: COLORS.black, fontSize: SIZES.small }}>
                {props.priority}
              </Text>
            </View>
            <View style={{ gap: 5 }}>
              <Text style={{ color: COLORS.grayText, fontWeight: 'bold' }}>
                {t('status')}:
              </Text>
              <Text
                style={{
                  color: COLORS.white,
                  backgroundColor: statusColor(),
                  padding: 10,
                }}
              >
                {props.status}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  containerStyle: {
    position: 'relative',
    marginBottom: 5,
  },
  contentContainer: {
    padding: SIZES.small,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priorityContainer: {
    position: 'absolute',
    left: 0,
    width: 10,
    height: '100%',
  },
  infoContainer: {
    padding: 10,
    gap: 3,
  },
  ticketTitle: {
    fontSize: SIZES.large,
    color: COLORS.blue,
    marginBottom: SIZES.large,
  },
});

export default TicketItem;
