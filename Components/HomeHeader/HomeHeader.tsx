import React from 'react';
import { View, Text, SafeAreaView } from 'react-native';

import { IMAGE_URL } from 'config';
import { t } from 'i18next';

import CustomAvatar from '@/Components/CustomAvatar';
import { useAuthStore } from '@/stores/useAuthStore';

const _date = new Date();
const HomeHeader = () => {
  const { user } = useAuthStore();
  const AMorPM = _date.toLocaleTimeString().split(' ');
  const avatarImage =
    user?.avatar && user?.avatar?.length > 0 ? IMAGE_URL + user?.avatar : undefined
  return (
    <SafeAreaView className="flex-row justify-between py-3">
      <View className="px-4 mb-5">
        <Text className="text-white text-3xl font-light ">
          {AMorPM.join(':').includes('AM') ? t('morning') : t('evening')}
        </Text>
        <Text className="text-3xl text-white font-bold">
          {user?.fullName || ''}
        </Text>
        <Text className=" text-gray font-bold">{user?.jobTitle || '-'}</Text>
      </View>
      <View className="px-4 mb-2 ">
        <CustomAvatar
          fullName={user?.fullName || '-'}
          avatar={avatarImage}
          size={80}
        />
      </View>
    </SafeAreaView>
  );
};

export default React.memo(HomeHeader);
