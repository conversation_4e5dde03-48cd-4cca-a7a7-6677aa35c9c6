import React, { memo } from 'react';
import { StyleSheet, Text, View, Dimensions } from 'react-native';

import { COLORS, SIZES } from 'Constants';

import { IUserRequests } from '../Components/RequestsList';
import i18n from '../i18n';

const RequestItem = ({ item }: { item: IUserRequests }) => {
  return (
    <View style={styles.requestItemContainer}>
      <Text style={styles.details}>
        {i18n.t('time')}{' '}
        <Text style={styles.value}>
          {new Date(item.createdAt).toLocaleTimeString()}
        </Text>
      </Text>
      <Text style={styles.details}>
        {i18n.t('date')}:{' '}
        <Text style={styles.value}>
          {new Date(item.createdAt).toDateString()}
        </Text>
      </Text>
      <Text style={styles.details}>
        {i18n.t('type')}: <Text style={styles.value}>{i18n.t(item.type)}</Text>
      </Text>
      <Text
        style={[
          styles.statusText,
          {
            backgroundColor: item.status === 'pending' ? 'orange' : 'teal',
          },
        ]}
      >
        {i18n.t(item.status)}
      </Text>
    </View>
  );
};

export default memo(RequestItem);

const { width } = Dimensions.get('screen');

const styles = StyleSheet.create({
  flashListContainer: {
    width: width,
    height: '100%',
    paddingHorizontal: SIZES.xSmall,
  },
  requestItemContainer: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray,
    marginBottom: 10,
    gap: 10,
    paddingVertical: 5,
  },
  details: {
    color: COLORS.primary,
    fontSize: SIZES.medium,
    fontWeight: '500',
  },
  value: {
    color: COLORS.secondary,
    fontWeight: '600',
  },
  listHeader: {
    fontWeight: '500',
    color: COLORS.primary,
    fontSize: SIZES.medium,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray,
    width: 205,
    marginVertical: 20,
  },
  statusText: {
    color: COLORS.white,
    fontWeight: 'bold',
    paddingHorizontal: SIZES.xSmall,
    paddingVertical: 5,
    width: 100,
    textAlign: 'center',
    borderRadius: 20,
    marginVertical: 5,
  },
});
