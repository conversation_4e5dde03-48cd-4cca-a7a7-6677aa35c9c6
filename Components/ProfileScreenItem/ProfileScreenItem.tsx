import React from 'react';
import { View, Text } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { Entypo } from '@expo/vector-icons';

import { COLORS } from '@/Constants';

interface profileScreenItem {
  icon: JSX.Element;
  title: string;
  textColor?: string;
  onPress: () => void;
}

const ProfileScreenItem = ({
  icon,
  title,
  textColor,
  onPress,
}: profileScreenItem) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      className="justify-between items-center w-full flex-row bg-[#FEFAFD] px-4 py-2"
    >
      <View className="flex-row gap-3 items-center py-2  ">
        <View className="bg-gray-100  rounded-full w-10 h-10 items-center justify-center ">
          {icon}
        </View>
        <Text className="text-lg font-semibold" style={{ color: textColor }}>
          {title}
        </Text>
      </View>
      <Entypo name="chevron-small-right" size={30} color={COLORS.gray2} />
    </TouchableOpacity>
  );
};

export default React.memo(ProfileScreenItem);
