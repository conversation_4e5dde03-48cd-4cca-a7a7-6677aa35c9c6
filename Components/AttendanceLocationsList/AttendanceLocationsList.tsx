import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View, Text, StyleSheet, Dimensions } from 'react-native';

import { FlashList } from '@shopify/flash-list';

import { useRouter } from 'expo-router';

import useGetUserLocations from '@/Hooks/locationHooks/useGetUserLocations';

import { IAttendanceLocations } from 'types/Index';

import { COLORS } from '@/Constants';
import AttendanceCard from '../AttendanceCard/AttendanceCard';

import EmptyLocationList from './EmptyLocationList';

const { width } = Dimensions.get('screen');

const AttendanceLocationsList = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const {
    data,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
  } = useGetUserLocations();

  const [loadingMore, setLoadingMore] = useState(false);

  const flatData = useMemo(() => {
    // If each page is { data: [...] }, extract .data from each
    if (!data?.pages) return [];
    return data.pages.flatMap(page =>
      Array.isArray(page?.data) ? page.data : []
    );
  }, [data]);

  const sortedData = useMemo((): IAttendanceLocations[] => {
    return flatData.slice().sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [flatData]);

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      setLoadingMore(true);
      fetchNextPage().finally(() => setLoadingMore(false));
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <FlashList
          showsVerticalScrollIndicator={false}
          onRefresh={handleRefresh}
          refreshing={isRefetching || isFetchingNextPage}
          ListEmptyComponent={EmptyLocationList}
          alwaysBounceVertical
          estimatedItemSize={200}
          ListHeaderComponent={
            <View style={styles.headerContainer}>
              <Text style={styles.headerTitle}>{t('attendance')}</Text>
            </View>
          }
          data={sortedData}
          keyExtractor={(item, index) => `${item._id}_${index}`}
          renderItem={({ item }) => {
            const startedAtDate = item?.startedAt
              ? new Date(item.startedAt)
              : null;
            const createdAtDate = item?.createdAt
              ? new Date(item.createdAt)
              : null;
            const updatedAtDate = item?.updatedAt
              ? new Date(item.updatedAt)
              : null;

            return (
              <AttendanceCard
                {...item}
                latency={item.latency}
                startedAt={
                  startedAtDate ? startedAtDate.toLocaleTimeString() : ''
                }
                endTime={
                  createdAtDate ? createdAtDate.toLocaleTimeString() : ''
                }
                endedAt={
                  updatedAtDate ? updatedAtDate.toLocaleDateString() : ''
                }
                onPress={() =>
                  item.location &&
                  item.location.lat &&
                  item.location.lng &&
                  router.push({
                    pathname: '(screens)/MapScreen',
                    params: item.location as any,
                  })
                }
              />
            );
          }}
          onEndReachedThreshold={0.3}
          onEndReached={handleLoadMore}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width,
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    marginTop: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },

  headerContainer: {
    marginBottom: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerAction: {
    fontSize: 20,
    color: COLORS.tertiary,
  },
});

export default AttendanceLocationsList;
