import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, Text, View } from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';

import MaterialIcons from '@expo/vector-icons/MaterialIcons';

const EmptyLocationList = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.emptyList}>
      <MaterialIcons name="location-off" size={24} color={Colors.danger} />
      <Text>{t('no-locations')}</Text>
    </View>
  );
};

export default memo(EmptyLocationList);

const styles = StyleSheet.create({
  emptyList: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
