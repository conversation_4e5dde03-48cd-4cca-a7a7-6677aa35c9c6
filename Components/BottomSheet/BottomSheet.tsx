import React, { Children } from 'react';
import { View, Text, Pressable } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

import { SCREEN_HEIGHT } from '@/Constants/Theme';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const BottomSheet = ({ toggleSheet, children, offset }) => {
  const pan = Gesture.Pan()
    .onChange(event => {
      const OVERDARG = 500;
      const offsetDelta = event.changeY + offset.value;
      const clamp = Math.max(-OVERDARG, offsetDelta);
      offset.value = offsetDelta > 0 ? offsetDelta : withSpring(clamp);
    })
    .onFinalize(ev => {
      if (offset.value < SCREEN_HEIGHT / 3) {
        offset.value = withSpring(0);
      } else {
        offset.value = withTiming(SCREEN_HEIGHT, {}, () => {
          runOnJS(toggleSheet);
        });
      }
    });

  const translateY = useAnimatedStyle(() => ({
    transform: [{ translateY: offset.value }],
  }));

  return (
    <Animated.View>
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        className="bg-black/10 absolute w-full h-full"
      >
        <AnimatedPressable
          style={{ opacity: 0.5 }}
          onPress={toggleSheet}
          className="w-full h-full"
        />
      </Animated.View>
      <GestureDetector gesture={pan}>
        <Animated.View
          entering={SlideInDown.springify().damping(15)}
          exiting={SlideOutDown}
          style={[translateY]}
          className="py-5 px-2 w-full bg-white z-10 "
        >
          {children}
        </Animated.View>
      </GestureDetector>
    </Animated.View>
  );
};

export default BottomSheet;
