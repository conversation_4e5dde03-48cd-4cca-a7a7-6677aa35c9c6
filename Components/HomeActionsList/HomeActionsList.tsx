import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from 'react-native';
import { Badge } from 'react-native-paper';

import { COLORS, SIZES } from '@/Constants';
import { SCREEN_WIDTH } from '@/Constants/Theme';
import Loading from '@/Layouts/Loading/Loading';
import { notificationsActions } from '@/Redux/Slices/NotificationsSlice';
import { useAppDispatch, useAppSelector } from '@/Redux/Store';

import { Ionicons } from '@expo/vector-icons';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface IHeaderActions {
  id: number;
  icon: any;
  title: string;
  path?: string;
}

const headerActions: IHeaderActions[] = [
  {
    id: 2,
    icon: (
      <MaterialCommunityIcons
        name="timetable"
        size={SIZES.xLarge * 1.3}
        color="green"
      />
    ),
    title: 'time-tables',
    path: 'AttendanceTablesScreen',
  },
  {
    id: 3,
    icon: (
      <MaterialCommunityIcons
        name="bell-outline"
        size={SIZES.xLarge * 1.3}
        color={COLORS.secondary}
      />
    ),
    title: 'notifications',
    path: 'NotificationScreen',
  },
  {
    id: 4,
    icon: <Ionicons name="location" size={SIZES.xLarge * 1.3} color="red" />,
    title: 'locations',
    path: 'LocationsScreen',
  },
  {
    id: 1,
    icon: (
      <Ionicons name="exit-outline" size={SIZES.xLarge * 1.3} color="orange" />
    ),
    title: 'requests',
    path: '/(user-requests)/LeaveRequestScreen',
  },
];

const HomeActionsList = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);

  const notificationsCount = useAppSelector(s => s.notifications.count);

  const handleClearNotifications = () => {
    dispatch(notificationsActions.clearNotification());
  };
  // get notifications from db
  // useEffect(() => {
  //   setLoading(true);
  //   axiosRequest({
  //     url: `/notifications?skip=${skipAndLimit.skip}&limit=${skipAndLimit.limit}`,
  //   })
  //     .then(response => {
  //       if (response.data) {
  //         dispatch(notificationsActions.setNotificationsList(response.data));
  //         setSkipAndLimit(prev => ({ ...prev, length: response.data.length }));
  //         dispatch(notificationsActions.incrementOrDecrement(+1));
  //       }
  //     })
  //     .catch(err => console.log(err))
  //     .finally(() => setLoading(false));
  // }, [dispatch, skipAndLimit.limit, skipAndLimit.skip]);

  return (
    <View style={styles.container}>
      {loading && <Loading />}
      <FlatList
        contentContainerStyle={styles.flatListContainer}
        data={headerActions}
        keyExtractor={item => String(item?.id)}
        horizontal={true}
        renderItem={({ item }) => {
          return (
            <TouchableOpacity
              style={styles.itemContainer}
              onPress={() => {
                if (item.title === 'notifications') {
                  handleClearNotifications();
                }
                router.push(`/(screens)/${item.path}`);
              }}
            >
              <View style={styles.iconContainer}>
                {item.title === 'notifications' && notificationsCount > 0 ? (
                  <View>
                    <Badge className="absolute left-7 top-0 bg-danger">
                      {notificationsCount}
                    </Badge>
                    {item.icon}
                  </View>
                ) : (
                  item.icon
                )}
              </View>
              <Text numberOfLines={1} style={styles.title}>
                {t(item.title)}
              </Text>
            </TouchableOpacity>
          );
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 9,
    marginHorizontal: 5,
    backgroundColor: COLORS.white,
    borderRadius: 10,
    alignSelf: 'center',
    justifyContent: 'center',
    height: 100,
  },
  flatListContainer: {
    flexGrow: 1,
    width: SCREEN_WIDTH - 20,
    justifyContent: 'space-evenly',
  },
  itemContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: SIZES.xSmall,
    marginHorizontal: SIZES.xSmall,
  },
  iconContainer: {
    paddingBottom: 6,
  },
  title: {
    textAlign: 'center',
    fontSize: SIZES.small,
    color: COLORS.primary,
    fontWeight: '500',
  },
});

export default memo(HomeActionsList);
