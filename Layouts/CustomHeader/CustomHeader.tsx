import React, { memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ViewStyle,
  TextStyle,
} from 'react-native';

import { Ionicons } from '@expo/vector-icons';

import { COLORS, SIZES } from '@/Constants';

interface ICustomHeaderProps {
  title?: string;
  onBackPress?: () => void;
  headerRight?: () => React.ReactNode;
  headerLeft?: () => React.ReactNode;
  showBackButton?: boolean;
  theme?: 'dark' | 'light';
  backgroundColor?: string;
  textStyle?: TextStyle;
  containerStyle?: ViewStyle;
}

const CustomHeader = ({
  title,
  onBackPress,
  headerRight,
  headerLeft,
  showBackButton = true,
  theme = 'light',
  backgroundColor,
  textStyle,
  containerStyle,
}: ICustomHeaderProps) => {
  // Determine text color based on theme
  const textColor = theme === 'dark' ? COLORS.white : COLORS.black;

  // Default background color if none is provided
  const headerBackgroundColor = backgroundColor || COLORS.primary;

  return (
    <View
      style={[
        styles.headerContainer,
        { backgroundColor: headerBackgroundColor },
        containerStyle, // Allow customization of container style
      ]}
    >
      <SafeAreaView style={{ flexDirection: 'row' }}>
        <View style={styles.leftContainer}>
          {/* Custom headerLeft or Back Button */}
          {headerLeft ? (
            headerLeft()
          ) : showBackButton ? (
            <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color={COLORS.white} />
            </TouchableOpacity>
          ) : null}
        </View>

        {/* Title */}
        <Text style={[styles.headerTitle, { color: COLORS.white }, textStyle]}>
          {title}
        </Text>

        {/* Custom headerRight */}
        <View style={styles.rightContainer}>
          {headerRight ? headerRight() : null}
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    minHeight: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.small,
    paddingVertical: SIZES.small,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 10,
  },
  headerTitle: {
    fontSize: SIZES.large,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1, // Center title in the middle of the header
  },
});

export default memo(CustomHeader);
