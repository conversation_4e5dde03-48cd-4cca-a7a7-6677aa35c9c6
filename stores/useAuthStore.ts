import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

import { getItem, setItem, deleteItemAsync } from 'expo-secure-store';

import { IUser } from '../types/Index';

type UserStore = {
  user: IUser | null;
  token: string | null;
  setUser: (user: IUser | null) => Promise<void>;
  setToken: (token: string | null) => Promise<void>;
  logout: () => Promise<void>;
  isLoggedIn: boolean;
};

export const useAuthStore = create<UserStore>()(
  persist(
    set => ({
      user: null,
      token: null,
      isLoggedIn: false,
      setUser: async user => set({ user, isLoggedIn: true }),
      setToken: async token => set({ token }),
      logout: async () => set({ user: null, token: null, isLoggedIn: false }),
    }),
    {
      name: 'auth-store',
      storage: createJSONStorage(() => ({
        getItem,
        setItem,
        removeItem: deleteItemAsync,
      })), // or use your own storage
      // partialize: state => ({ user: state.setUser, token: state.setToken }),
    }
  )
);
