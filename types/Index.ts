export enum LEAVE_TYPES {
  LEAVE = 'leave',
  ABSENT = 'absent',
  OTHER = 'other',
}

export interface IUser {
  avatar: string;
  fullName: string;
  phone: string;
  role: string;
  _id: string;
  jobTitle: string;
  jobDescription: string;
  section: Section;
  locations: Location[];
  email: string;
  address: string;
  db: string;
  company: Company;
  timeTables: TimeTable[];
  branches: any[];
  accessCode: number;
}

export interface Company {
  _id: string;
  userName: string;
  companyName: string;
  password: string;
  dbName: string;
  dbUserName: string;
  dbPassword: string;
  numberOfUsers: number;
  numberOfCreatedUsers: number;
  companyKey: string;
  taxNumber: string;
  address: string;
  email: string;
  phone: string;
  branches: any[];
  allowedBranches: boolean;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Location {
  _id: string;
  lat: string;
  lng: string;
  radius: string;
  title: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Section {
  _id: string;
  title: string;
  timetables: string[];
  locations: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TimeTable {
  _id: string;
  title: string;
  periods: Period[];
  startDate: null;
  endDate: null;
  daysOfWeek: string[];
  disabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Period {
  _id: string;
  startTime: string;
  endTime: string;
  startNotice: string;
  endNotice: string;
  timetable?: string;
  disabled: boolean;
  daysOfWeek: string[];

  createdAt: Date;
  updatedAt: Date;
}

export interface ILocations {
  _id: string;
  user: string;
  location: {
    _id: string;
    lat: number;
    lng: number;
    radius: number;
    title: string;
    description: string;
  };
  attendedBy: string;
  startedAt: Date;
  status: 'attend' | 'absent';
  absent: boolean;
  day: string;
  dayName: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IAttendanceLocations {
  absent: boolean;
  day: string;
  user: string;
  _id: string;
  attendedBy: 'app' | 'admin';
  dayName: 'Mon' | 'Tue' | 'Wed' | 'Thu' | 'Fri' | 'Sat' | 'Sun';
  status: 'attend' | 'absent';
  location: null | ILocation;
  latency: string;
  createdAt: Date;
  startedAt: Date;
  updatedAt: Date;
}

export interface ILocation {
  _id: string;
  createdAt: Date;
  description: string;
  lat: number;
  lng: number;
  radius: string;
  title: string;
  updatedAt: Date;
}

// USER ATTEND OR LEAVE REQUESTS

export interface IUserRequests {
  _id: string;
  user: IUser;
  startDate: Date;
  endDate: Date;
  description: string;
  type: string;
  status: 'pending' | 'accepted' | 'rejected';
  senderRole: string;
  createdAt: Date;
  updatedAt: Date;
}
