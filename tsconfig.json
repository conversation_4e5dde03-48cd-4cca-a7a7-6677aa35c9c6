{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true, // Enables strict type-checking options
    "esModuleInterop": true, // Enables emit interoperability between CommonJS and ES Modules
    "skipLibCheck": true, // Skip type checking of declaration files
    "forceConsistentCasingInFileNames": true, // Ensure consistent file naming
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["src/**/*", "app/**/*", "declarations.d.ts", "Components/**/*"],
  "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts"]
}
