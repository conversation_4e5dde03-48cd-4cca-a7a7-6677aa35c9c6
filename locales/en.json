{"CFBundleDisplayName": "English", "NSContactsUsageDescription": "Application Supports English language", "welcome-to": " Welcome To", "welcome": "Welcome", "mobile-app": "Mobile Attendance Application", "app-name": "WAS", "inputs": {"password": "Password", "email": "Email", "phone": "Phone", "confirmPassword": "Confirm Password", "Address": "Address", "Dont't Have an Account": "Dont't Have an Account", "Login": "<PERSON><PERSON>"}, "greetings": {"Morning": "Morning"}, "workSchaduel": "work schduel", "ChekIn": "ChekIn", "Attendance List": "Attendance List", "Attendance Correction": "Attendance Correction", "On Duty": "On Duty", "Leave": "Leave", "mobile-attendance-application": "Mobile Attendance Application", "user-phone": " Phone number", "password": "Password", "login": "<PERSON><PERSON>", "phone-must-be-at-least-8-characters": "Phone must be at least 8 characters", "password-must-be-at-least-6-characters": "Password must be at least 6 characters", "company-key-must-be-at-least-2-characters": "Company key must be at least 2 characters", "attend-time": "Attend time", "leave-time": "Leave time", "return-time": "Return time", "attend-notice": "Attend Notice", "leave-notice": "Leave notice", "period": "Period", "start-date": "Start date", "end-date": "End date", "time-table": "Time table", "attendance": "Attendance", "shrink": "Shrink", "view-all": "View all", "working-schedule": "Working Schedule", "no-locations": "No locations", "no-data-to-show": "No data to be shown", "company-key": "Company key", "save": "Save", "choose-from-gallery": "Choose from gallery", "delete": "Delete", "take-a-photo": "Take a photo", "user-name": "Username", "address": "Address", "phone": "Phone", "email": "Email", "application-language": "Application language", "language": "Language", "fast-access": "Fast access", "face-id": "FaceID", "settings": "Settings", "switch-language": "Switch language", "something-had-gone-wrong": "Something had gone wrong", "edit-success": "Edit success", "submit": "Submit", "prev-request": "Previous requests", "username": "Username", "morning": "Morning", "evening": "Evening", "request-type-is-required": "Request type is required", "request-note-is-required": "Request note is required", "none": "None", "leave": "Leave", "absent": "Absent", "other": "Other", "note": "Note", "write-your-reason": "Write your leave reason", "date-time-error": "Start date/time must be before Leave date/time", "my-requests": "My requests", "previous-requests": "Previous requests", "check-in": "Check In", "pending": "Pending", "accepted": "accepted", "rejected": "Rejected", "locations": "Locations", "time-tables": "Time tables", "notifications": "Notifications", "requests": "Requests", "no-messages": "No messages", "accept": "Accept", "ticket-actions": "Ticket Actions", "reject": "Reject", "messages": "Messages", "no-tasks": "No tasks", "no-notifications": "No notification", "something-went-wrong": "Something went wrong", "first-attend": "First attend", "last-attend": "End attend", "no-replays": "No replay", "profile": "profile", "terms-and-conditions": "Terms & conditions", "privacy-and-policy": "Privacy & policy", "sign-out": "Sign out", "sorry-you-are-not-in-valid-location": "You are not in a valid location", "sorry": "Sorry", "you-are-not-authenticated": "You are not authenticated", "please-re-login": "please Re-Login", "re-try-auth-print": "Re try Biometric print", "you-have-been-registered": "You have been registered", "in": "in", "delete-success": "Delete success", "Submit": "Submit", "user-requests": "User Requests", "All-requests": "All Requests"}