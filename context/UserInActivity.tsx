import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';
import { AppState } from 'react-native';

const LOCK_TIME = 100000;

const UserInActivityProvider = ({ children }: any) => {
  const appState = React.useRef(AppState.currentState);

  React.useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription.remove();
  }, []);

  const handleAppStateChange = async (nextAppState: any) => {
    console.log('AppState', appState.current, nextAppState);

    if (nextAppState === 'inactive') {
      // navigation.navigate("WhiteScreen");
    }

    if (nextAppState === 'background') {
      // disconnectIo();
      // recordStartTime();
    } else if (
      nextAppState === 'active' &&
      appState.current.match('/background/')
    ) {
      // const asyncStorageTime = await AsyncStorage.getItem("startTime");
      // const elapsed = Date.now() - Number(asyncStorageTime) || 0;
      // if (elapsed >= LOCK_TIME) {
      //   navigation.navigate("LockScreen");
      // }
    }

    appState.current = nextAppState;
  };

  const recordStartTime = async () => {
    await AsyncStorage.setItem('startTime', JSON.stringify(Date.now()));
  };

  return children;
};

export default UserInActivityProvider;
