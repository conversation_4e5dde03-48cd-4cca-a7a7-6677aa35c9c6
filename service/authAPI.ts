import { BASE_URL } from '@/config';
import axios from 'axios';

import { handleError } from '../utils/errors/errorHandler';

import { IUser } from 'types/Index';

export interface ILoginPayload {
  phone: string;
  password: string;
  companyKey: string;
}

interface ILoginResponse {
  user: IUser;
  token: string;
}
export const loginAPI = async (
  payload: ILoginPayload
): Promise<ILoginResponse | null> => {
  try {
    const response = await axios.post(`${BASE_URL}/login`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
    return null;
  }
};
