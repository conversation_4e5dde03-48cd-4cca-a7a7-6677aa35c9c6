import axiosInstance from '../utils/axios/axiosInstance';
import { handleError } from '../utils/errors/errorHandler';

import { IAttendanceLocations } from 'types/Index';

type getAttendancePayload = {
  skip?: number;
  limit?: number;
};

export type IGetUserAttendanceLocations = {
  data: IAttendanceLocations[];
  length: number;
};

export const getUserAttendanceLocationsAPI = async ({
  skip,
  limit = 5,
}: getAttendancePayload) => {
  try {
    const response = await axiosInstance.get(
      `/attendance?skip=${skip ?? 0}&limit=${limit}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching attendance locations:', error);

    handleError(error);
  }
};
