import { BASE_URL } from '@/config';
import { useAuthStore } from '@/stores/useAuthStore';
import axios from 'axios';

import { getItem } from 'expo-secure-store';

// Create an axios instance with the base URL
const axiosInstance = axios.create({
  baseURL: `${BASE_URL}/api`,
});

// Add a request interceptor to dynamically add the token
axiosInstance.interceptors.request.use(
  async config => {
    try {
      if (__DEV__) {
        const fullURL = BASE_URL + config.url;
        console.log('Request sent to:', fullURL, config.data);
      }

      const token = useAuthStore.getState().token;

      // If a token exists, attach it to the Authorization header
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error retrieving token:', error);
    }

    return config;
  },
  error => {
    // Handle errors before the request is sent
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle responses globally
axiosInstance.interceptors.response.use(
  response => {
    // Pass through successful responses
    return response;
  },
  error => {
    // Handle errors globally
    if (error.response && error.response.status === 403) {
      console.error('Unauthorized');
    }
    console.error('Response error:', error);
    return Promise.reject(error);
  }
);

export default axiosInstance;
