import AsyncStorage from '@react-native-async-storage/async-storage';
import { ALERT_TYPE, Toast } from 'react-native-alert-notification';

import axios, { AxiosRequestConfig } from 'axios';

import { router } from 'expo-router';

import { BASE_URL } from '../config';

import { connectIo, socket } from './useSocket';

let token: string | null = null;
export const updateToken = (v: string | null) => {
  token = v;
};

const getToken = async (): Promise<any> => {
  try {
    token = await AsyncStorage.getItem('token');
    let formattedToken: string | null = null;
    if (token) {
      formattedToken = token?.replace(/"/g, ''); // Remove any extra quotes
    }
    return formattedToken;
  } catch (error) {
    console.warn(error);
  }
};

export const axiosRequest = async (
  options: AxiosRequestConfig
): Promise<any> => {
  try {
    token = await getToken();

    if (token) {
      const response = await axios({
        baseURL: BASE_URL,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        ...options,
      });

      return response;
    }
  } catch (error: any) {
    let status = error?.response?.status;

    const forbidden = error?.response?.status === 403;
    const unAuthorized = error?.response?.status === 401;

    if (unAuthorized) {
      // Perform token refresh
      const refreshResponse = await axios.get(`${BASE_URL}/refresh`, {
        withCredentials: true,
      });
      console.log('REFRESH RESPONSE', refreshResponse);
      socket.auth = cb => cb({ token: refreshResponse.data?.token });
      AsyncStorage.setItem('token', refreshResponse.data?.token);
      if (!socket.connected) {
        connectIo();
      }
      // Retry the original request with the new token
      return axiosRequest({
        ...options,
        headers: {
          ...(options.headers || {}),
          Authorization: `Bearer ${refreshResponse.data.token}`,
        },
      });
    }
    if (forbidden) {
      // Remove authentication data if not authenticated

      Toast.show({
        title: 'You Are Not Authenticated',
        type: ALERT_TYPE.DANGER,
        autoClose: 3000,
        onHide: () => {
          AsyncStorage.removeItem('token');
          router.replace('/(auth)');
        },
      });
    }
    console.log(
      `status`,
      status,
      'url',
      options.url,
      'Error',
      JSON.stringify(error?.response?.data)
    );
    return error;
  }
};
