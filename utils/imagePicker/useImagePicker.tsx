import { Alert, Linking } from 'react-native';

import * as MediaLibrary from 'expo-media-library';

const useImagePicker = () => {
  const [libraryPermission, requestLibraryPermission] =
    MediaLibrary.usePermissions();

  const handleImagePicker = async () => {
    const { status } = await requestLibraryPermission();
    if (status !== 'granted') {
      Alert.alert(t('permission-required'));
      Linking.openSettings();
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled && result.assets?.length) {
      const selectedImageUri = result.assets[0].uri;
      const imageUri =
        Platform.OS === 'android'
          ? selectedImageUri
          : selectedImageUri.replace('file://', '');

      const formData = new FormData();
      formData.append('avatar', {
        uri: imageUri,
        name: 'photo.jpg',
        type: 'image/jpeg',
      });

      try {
        await axiosRequest({
          method: 'PUT',
          url: `/users/${user?._id}`,
          data: formData,
        });
        setForm(prev => ({ ...prev, avatar: selectedImageUri }));
        setOutsource(true);
        setBottomSheetVisible(false);
      } catch (error) {
        console.error('Error uploading image', error);
      }
    }
  };

  return {};
};

export default useImagePicker;
