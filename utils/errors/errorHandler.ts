import axios from 'axios';
import { t } from 'i18next';

import * as SecureStore from 'expo-secure-store';

import { SecureStoreKeys } from '@/Constants/secureStoreKeys';
import axiosInstance from '../axios/axiosInstance';
import { customToast } from '../toast';

// Logout function to clear local storage and redirect
// const logout = async () => {
//   await SecureStore.deleteItemAsync(SecureStoreKeys.token);
//   await SecureStore.deleteItemAsync(SecureStoreKeys.refresh);
// customToast(t('session-expired-login-again'), 'info');
//   // Redirect to login screen or perform other logout actions
// };

// Function to handle Axios errors
export const handleError = async (error: unknown) => {
  // Check if the error is from Axios
  if (axios.isAxiosError(error)) {
    const err = error.response || error;

    // Handle 401 or 403 (Unauthorized or Forbidden)
    if (err?.status === 401 || err?.status === 403) {
      const refresh = await SecureStore.getItemAsync(SecureStoreKeys.refresh);

      if (refresh) {
        const cleanRefreshToken = refresh.replace(/^"|"$/g, ''); // Remove quotes
        try {
          // Try refreshing the token
          const refreshResponse = await axiosInstance.post('/refresh', {
            refresh: cleanRefreshToken,
          });

          const { token, refresh: newRefreshToken } = refreshResponse.data;

          // Update tokens in SecureStore
          await SecureStore.setItemAsync(SecureStoreKeys.token, token);
          await SecureStore.setItemAsync(
            SecureStoreKeys.refresh,
            newRefreshToken
          );

          // Retry the original request with the new token
          if (error.config) {
            error.config.headers['Authorization'] = `Bearer ${token}`;
            return axiosInstance.request(error.config); // Retry original request
          }
        } catch (refreshError) {
          if (axios.isAxiosError(refreshError)) {
            // Refresh token is invalid
            if (
              refreshError.response?.status === 401 ||
              refreshError.response?.status === 403
            ) {
              customToast(t('invalid-token-login-again'), 'error');
              // await logout()
            }
          } else {
            // customToast(t('unable-to-refresh-token'), 'error');
            console.error('Refresh token error:', refreshError);
          }
        }
      } else {
        // No refresh token available
        customToast(t('session-expired-login-again'), 'error');
        // await logout();
      }
    } else {
      // Handle other Axios errors
      const errorMessage =
        ('data' in err ? err.data?.error?.message : undefined) ||
        ('data' in err ? err.data?.message : undefined) ||
        error.message ||
        t('unexpected-error-occurred');
      customToast(errorMessage, 'error');
    }
    return err; // Return the error for further handling
  } else {
    // Handle non-Axios errors
    customToast(t('unknown-error-occurred'), 'error');
    console.error('Non-Axios error:', error);
  }
};
