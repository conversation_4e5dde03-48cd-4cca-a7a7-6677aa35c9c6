export function formatDate(dateString) {
  // Create a Date object from the input string
  const date = new Date(dateString);

  // Define arrays for days and months
  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  // Get day, date, month, and year components
  const dayOfWeek = daysOfWeek[date.getUTCDay()];
  const dayOfMonth = date.getUTCDate();
  const month = months[date.getUTCMonth()];
  const year = date.getUTCFullYear();

  // Format the result string
  const formattedDate = `${dayOfWeek},${dayOfMonth},${month},${year}`;

  return formattedDate;
}
