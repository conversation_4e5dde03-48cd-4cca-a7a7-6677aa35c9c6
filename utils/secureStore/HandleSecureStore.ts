import * as SecureStore from 'expo-secure-store';

import { SecureStoreKeys } from '@/Constants/secureStoreKeys';

const getUserId = async (): Promise<string | null> => {
  const userId = await SecureStore.getItemAsync(SecureStoreKeys.userId);
  return userId;
};

const setUserId = async (userId: string): Promise<void> => {
  await SecureStore.setItemAsync(SecureStoreKeys.userId, userId);
};

const removeUserId = async (): Promise<void> => {
  await SecureStore.deleteItemAsync(SecureStoreKeys.userId);
};

// Token
const getToken = async (): Promise<string | null> => {
  const token = await SecureStore.getItemAsync(SecureStoreKeys.token);
  return token;
};

const setToken = async (token: string): Promise<void> => {
  await SecureStore.setItemAsync(SecureStoreKeys.token, token);
};

const removeToken = async (): Promise<void> => {
  await SecureStore.deleteItemAsync(SecureStoreKeys.token);
};

const GetItemFromSecureStore = async (
  item: keyof typeof SecureStoreKeys
): Promise<string | null> => {
  return await SecureStore.getItem(item);
};
const AddItemToSecureStore = async (
  key: keyof typeof SecureStoreKeys,
  value: string
): Promise<void> => {
  await SecureStore.setItemAsync(key, value);
};
const removeItemFromSecureStore = async (
  key: keyof typeof SecureStoreKeys
): Promise<void> => {
  await SecureStore.deleteItemAsync(key);
};

/**
 * Check if the stored token is valid
 * This performs a basic validation check (not expired, properly formatted)
 * @returns True if the token appears valid, false otherwise
 */

const isTokenValid = async (): Promise<boolean> => {
  try {
    const token = await getToken();

    // Basic JWT structure check (header.payload.signature)
    const parts = token?.split('.');
    if (parts?.length !== 3) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
};

export {
  isTokenValid,
  getUserId,
  setUserId,
  removeUserId,
  getToken,
  setToken,
  removeToken,
  AddItemToSecureStore,
  removeItemFromSecureStore,
  GetItemFromSecureStore,
};
