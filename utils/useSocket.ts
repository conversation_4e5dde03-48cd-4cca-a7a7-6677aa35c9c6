import AsyncStorage from '@react-native-async-storage/async-storage';

import { io } from 'socket.io-client';

export const events = {
  socket: {
    connect: 'connect',
    disconnect: 'disconnect',
    connection: 'connection',
    connect_error: 'connect_error',
  },
  users: {
    new: {
      client: 'newUser',
    },
    leave: {
      client: 'userLeave',
    },
  },
  mediasoup: {
    transport: {
      create: {
        server: 'createTransport',
        client: 'transportInfo',
      },
      connect: {
        server: 'connectTransport',
        client: 'connectTransportInfo',
      },
      close: {
        server: 'closeTransport',
        client: 'closeTransportInfo',
      },
    },
    produce: {
      server: 'produce',
      client: 'produceInfo',
    },
    consume: {
      server: 'consume',
      client: 'consumeInfo',
    },
    router: {
      capabilities: {
        server: 'getRouterCapabilities',
        client: 'routerRtpCapabilities',
      },
    },
    rtp: {
      capabilities: {
        server: 'getRtpCapabilities',
        client: 'rtpCapabilities',
      },
    },
  },
  rooms: {
    join: {
      server: 'joinRoom',
      client: 'joinRoom',
    },
    create: {
      server: 'createRoom',
      client: 'createdRoom',
    },
    leave: {
      server: 'leaveRoom',
      client: 'roomLeaved',
    },
  },
  messages: {
    test: {
      server: 'msg',
      client: 'incomingMessage',
    },
  },
};

// "undefined" means the URL will be computed from the `window.location` object
const SocketURI = 'https://attendance.wafitop.com';
const getToken = async () => {
  try {
    const token = await AsyncStorage?.getItem('token');
    if (token) return token;
  } catch (error) {
    console.error(error);
    return error;
  }
};

export const socket = io(SocketURI, {
  secure: true,
  rejectUnauthorized: false,
  autoConnect: true,
  reconnection: true, // Enable reconnection
  reconnectionAttempts: 5, // Number of reconnection attempts
  reconnectionDelay: 1000, // Delay between attempts
  reconnectionDelayMax: 5000, // Max delay
  auth: cb => {
    AsyncStorage.getItem('token').then(token => {
      cb({
        token: `Bearer ${token?.replace(/"/g, '')}`,
      });
    });
  },
});

export const connectIo = () => socket.connect();
export const disconnectIo = () => socket.disconnect();

const initializeSocket = async () => {
  const token = (await getToken()) as string;
  socket.auth = { token: `Bearer ${token?.replace(/"/g, '')}` };
  socket.connect();
};
initializeSocket();

const handleConnection = async (data: any) => {
  console.log('socket connection', data);
};

const handleConnect = async () => {
  console.log('connected ...........');
};

socket.on('reconnect_attempt', () => {
  console.log('Attempting to reconnect...');
});

socket.on('reconnect', attemptNumber => {
  console.log(`Reconnected successfully on attempt ${attemptNumber}`);
});

socket.on('reconnect_error', error => {
  console.error('Reconnection failed: ', error);
});

const handleDisconnect = async (reason: string) => {
  console.log('Disconnected', reason);
};

let retryCount = 0;
const maxRetries = 10;

export const handleConnectError = async (error: any) => {
  if (retryCount >= maxRetries) {
    console.error(
      'Max retry attempts reached. Stopping reconnection attempts.'
    );
    return; // Stop further attempts after 5 retries
  }

  if (error.message.includes('Invalid token')) {
    try {
      // Refresh token logic or ask the user to re-authenticate
      const refreshedToken = (await getToken()) as string;
      socket.auth = { token: `Bearer ${refreshedToken?.replace(/"/g, '')}` };
      socket.connect(); // Reconnect with the refreshed token
      retryCount++; // Increment retry counter
    } catch (refreshError: any) {
      console.error('Failed to refresh token:', refreshError.message);
      retryCount++; // Increment retry counter
      // Optionally navigate to the login screen
    }
  } else {
    console.error('Connection error:', error.message);
  }
};

socket.on(events.socket.connection, handleConnection);
socket.on(events.socket.connect, handleConnect);
socket.on(events.socket.disconnect, handleDisconnect);
socket.on(events.socket.connect_error, handleConnectError);
socket.on('error', handleConnectError);

export const promisifyEmit = (event = '', ...args: any[]) =>
  new Promise((resolve, reject) => {
    socket.emit(event, ...args, function (result: any) {
      // console.log(event, result, ...args);
      return result?.error ? reject(result) : resolve(result);
    });
  });
