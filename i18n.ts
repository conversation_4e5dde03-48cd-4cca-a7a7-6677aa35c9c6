import { initReactI18next } from 'react-i18next';

// Import the polyfill at the very beginning
import i18n from 'i18next';

import ar from './locales/ar.json';
import en from './locales/en.json';

i18n.use(initReactI18next).init({
  lng: 'en',
  fallbackLng: 'en',
  resources: {
    en,
    ar,
  },
  interpolation: {
    escapeValue: false, // react already safes from xss
  },
});

export default i18n;
