{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "jsxSingleQuote": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "auto", "importOrder": ["react", "<THIRD_PARTY_MODULES>", "expo", "components/", "screens/", "Hooks/", "Context/", "utils/", "styles/", "assets/", "types/", "^../(.*)$", "^[./]"], "importOrderSeparation": true, "plugins": ["@trivago/prettier-plugin-sort-imports"], "organizeImportsSkipDestructiveCodeActions": true}