export const socketEventsList = {
  tickets: {
    create: {
      on: 'ticket-created',
    },
    statusChange: {
      on: 'ticket-status-change',
    },
  },
  requests: {
    create: {
      on: 'new-request-created',
    },
    statusChange: {
      on: 'request-status-change',
    },
  },
  notifications: {
    seen: {
      emit: 'notification-seen',
    },
  },
  messages: {
    create: {
      emit: 'msg',
      on: 'msg-received',
    },
  },
  replays: {
    create: {
      emit: 'send-replay',
      on: 'receive-replay',
    },
  },
} as const;

export type SocketEmitValues = {
  [K in keyof typeof socketEventsList]: {
    [L in keyof (typeof socketEventsList)[K]]: (typeof socketEventsList)[K][L] extends {
      emit: infer O;
    }
      ? O
      : never;
  }[keyof (typeof socketEventsList)[K]];
}[keyof typeof socketEventsList];

export type SocketListenValues = {
  [K in keyof typeof socketEventsList]: {
    [L in keyof (typeof socketEventsList)[K]]: (typeof socketEventsList)[K][L] extends {
      on: infer O;
    }
      ? O
      : never;
  }[keyof (typeof socketEventsList)[K]];
}[keyof typeof socketEventsList];
