// user id Reports

/*
reports required 
1- user by id attendance report 
2- day report [all all attends by date ]  (paginated); 
3- section git all section attendance rate ( total attend )
4- section details by id for attendance
5- requests report (leave - absent - other ) 
6- un-assigned users ( done ); with all it's filters 
7- totals by employee id 
*/

//[1]- attendance by date
// => it will contain array of users each user carry data of his day attendance ;

interface TUserAttendanceReport {
  fullName: string;
  avatar: string;
  _id: string; // refers to user ID ;
  attendData: {
    startTime: Date;
    endTime: Date;
    latencyTime: Number; // by minutes
    overTime: Number; // by minutes
    Day: string; // example sat
    date: Date;
    absent?: boolean;
    leaveRequest: boolean;
  }[];
}

interface ISectionAttendanceReportByID {
  _id: string; // section id
  title: string;
  numberOfEmployees: number;
  employees: { fullName: string; _id: string; inTimeRate: number }[];
  // inTimeRate: example "65%";
}
[];

interface IRequestsReport {
  fullName: string;
  _id: string;
  requests: { type: string; notes: string; accepted: boolean }[];
}

interface ITotalsForEmployeeByID {
  totalAbsents: number;
  totalLeaves: number;
  totalAcceptedLeaves: number;
  totalAcceptedAbsents: number;
}
